#!/bin/sh

if  [ -z "$BACKEND_API_URL" ]; then
    echo "Please setup env variables: BACKEND_API_URL"
    exit 1
fi

cd /usr/share/nginx/html/vEdge/services

BACKEND_API_URL_ESCAPED=$(printf '%s\n' "$BACKEND_API_URL" | sed -e 's/[\/&]/\\&/g')

echo "Replacing REPLACE_WITH_BACKEND_API_URL with escaped $BACKEND_API_URL_ESCAPED"
sed -i -- "s/<REPLACE_WITH_BACKEND_API_URL>/$BACKEND_API_URL_ESCAPED/g" *.js
