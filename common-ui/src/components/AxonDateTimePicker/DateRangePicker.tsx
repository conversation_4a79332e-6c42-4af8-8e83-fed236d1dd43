import * as React from 'react';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { DayPickerRangeProps } from 'react-day-picker';

import { addPrefixToClassNames, cn } from '@/utils';
import { AxonButton, AxonCalendar, AxonPopover, AxonPopoverContent, AxonPopoverTrigger } from '@/components';
type IProps = Omit<DayPickerRangeProps, 'mode'> & {
  showIcon?: boolean;
  formatStr?: string;
  showCustom?: boolean;
  placeholder?: React.ReactNode;
  className?: {
    button?: string;
  };
};

export function AxonDateRangePicker(props: IProps) {
  const {
    className,
    showIcon = true,
    selected,
    onSelect,
    formatStr = 'dd-MM-yyyy',
    showCustom = true,
    placeholder = 'Pick a date',
    classNames,
    ...rest
  } = props;
  return (
    <div className={cn('grid gap-2', addPrefixToClassNames([className]))}>
      <AxonPopover>
        <AxonPopoverTrigger asChild>
          <AxonButton
            id='date'
            variant={'outline'}
            className={cn(
              'h-8 w-fit justify-start text-left font-normal',
              !selected && 'text-muted-foreground',
              classNames?.button,
            )}>
            {showIcon && <CalendarIcon className='size-4' />}
            {showCustom && selected?.from ? (
              selected.to ? (
                <>
                  {format(selected.from, formatStr)} - {format(selected.to, formatStr)}
                </>
              ) : (
                format(selected.from, formatStr)
              )
            ) : (
              <span>{placeholder}</span>
            )}
          </AxonButton>
        </AxonPopoverTrigger>
        <AxonPopoverContent className={cn('w-auto p-0')} align='start'>
          <AxonCalendar
            initialFocus
            defaultMonth={selected?.from}
            selected={selected}
            onSelect={onSelect}
            numberOfMonths={2}
            {...rest}
            mode='range'
          />
        </AxonPopoverContent>
      </AxonPopover>
    </div>
  );
}
