import cameraBase from './images/devices/cameraBase.png';
import car from './images/devices/car.png';
import computer from './images/devices/computer.png';
import cordlessPhone from './images/devices/cordlessPhone.png';
import doorbell from './images/devices/doorbell.png';
import extender from './images/devices/extender.png';
import gameConsole from './images/devices/gameConsole.png';
import genericIot from './images/devices/genericIot.png';
import headphones from './images/devices/headphones.png';
import homeSecure from './images/devices/homeSecure.png';
import homeTheatre from './images/devices/homeTheatre.png';
import lamp from './images/devices/lamp.png';
import laptop from './images/devices/laptop.png';
import mediaPlayer from './images/devices/mediaPlayer.png';
import printer from './images/devices/printer.png';
import router from './images/devices/router.png';
import setTopBox from './images/devices/setTopBox.png';
import smartphone from './images/devices/smartphone.png';
import smartWatch from './images/devices/smartWatch.png';
import speakers from './images/devices/speakers.png';
import strorageDevice from './images/devices/strorageDevice.png';
import tablet from './images/devices/tablet.png';
import thermoStat from './images/devices/thermoStat.png';
import tv from './images/devices/tv.png';
import unknown from './images/devices/unknown.png';
import wifiAp from './images/devices/wifiAp.png';
import wifiMeshNode from './images/devices/wifiMeshNode.png';
import DTLogo from './images/dt-logo-bg.png';
import MagentaTV from './images/MagentaTv.png';
import Telefonie from './images/telefonie-logo.png';

export function getDeviceImage(deviceTypeStr: string) {
  const deviceImageMap: Record<string, string> = {
    Alcatel_Phone: smartphone,
    Amazon: genericIot,
    Amazon_Echo: smartWatch,
    Android: smartphone,
    Apple: smartphone,
    Apple_TV: tv,
    AV_Receiver: homeTheatre,
    BlackBerry: smartphone,
    Blu_Phone: smartphone,
    BluRay_Player: mediaPlayer,
    BQ_Phone: smartphone,
    Car: car,
    Chromecast: mediaPlayer,
    Data_Storage: strorageDevice,
    Docking_Station: laptop,
    Fire_TV: tv,
    Game_Console: gameConsole,
    Huawei_Phone: smartphone,
    iMac: computer,
    IoT: genericIot,
    IP_Camera: cameraBase,
    iPad: tablet,
    iPhone: smartphone,
    iPod: mediaPlayer,
    Kindle: tablet,
    Laptop: laptop,
    LG_Phone: smartphone,
    Light_Wifi: lamp,
    Lighting: lamp,
    'Mac-Mini': computer,
    MacBook: laptop,
    Magic_Jack: cordlessPhone,
    Media_Streaming: mediaPlayer,
    Mini_PC: computer,
    Motorola_Phone: smartphone,
    Music_Player: mediaPlayer,
    Nintendo: gameConsole,
    Octoscope: genericIot,
    OnePlus_Phone: smartphone,
    Optik_TV_STB: setTopBox,
    PC: computer,
    Phone: cordlessPhone,
    Pik_TV_STB: setTopBox,
    PlayStation: gameConsole,
    Plug: genericIot,
    Powerline_Adapter: genericIot,
    Printer: printer,
    Remote_Controller: genericIot,
    Router: router,
    Samsung_Device: unknown,
    Samsung_Phone: smartphone,
    Security: doorbell,
    SetTop_Box: setTopBox,
    SIP_Adapter: genericIot,
    Smart_Display: tablet,
    Smart_Home_Hub: homeSecure,
    Smart_Scale: genericIot,
    Smart_Speaker: speakers,
    Tablet: tablet,
    Thermostat: thermoStat,
    TV: tv,
    UNKNOWN: unknown,
    VR_Headset: headphones,
    Watch: smartWatch,
    WiFi_AP: wifiAp,
    WiFi_Extender: extender,
    Extender: extender,
    WiFi_Mesh_Node: wifiMeshNode,
    Wii: gameConsole,
    Windows_Phone: smartphone,
    XBox: gameConsole,
    DT: DTLogo,
    MagentaTV: MagentaTV,
    Telefonie: Telefonie,
  };

  return deviceImageMap[deviceTypeStr] || unknown;
}
