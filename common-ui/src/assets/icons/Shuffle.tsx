import React from 'react';

export const Shuffle = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='17' viewBox='0 0 16 17' fill='none' {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.167 2.5C10.167 2.22386 10.3909 2 10.667 2H14.0003C14.2765 2 14.5003 2.22386 14.5003 2.5V5.83333C14.5003 6.10948 14.2765 6.33333 14.0003 6.33333C13.7242 6.33333 13.5003 6.10948 13.5003 5.83333V3.70711L3.02055 14.1869C2.82528 14.3821 2.5087 14.3821 2.31344 14.1869C2.11818 13.9916 2.11818 13.675 2.31344 13.4798L12.7932 3H10.667C10.3909 3 10.167 2.77614 10.167 2.5ZM2.31344 2.8131C2.5087 2.61784 2.82528 2.61784 3.02055 2.8131L6.35388 6.14644C6.54914 6.3417 6.54914 6.65828 6.35388 6.85354C6.15862 7.0488 5.84203 7.0488 5.64677 6.85354L2.31344 3.52021C2.11818 3.32495 2.11818 3.00836 2.31344 2.8131ZM9.64678 10.1464C9.84204 9.95118 10.1586 9.95118 10.3539 10.1464L13.5003 13.2929V11.1667C13.5003 10.8905 13.7242 10.6667 14.0003 10.6667C14.2765 10.6667 14.5003 10.8905 14.5003 11.1667V14.5C14.5003 14.7761 14.2765 15 14.0003 15H10.667C10.3909 15 10.167 14.7761 10.167 14.5C10.167 14.2238 10.3909 14 10.667 14H12.7932L9.64678 10.8536C9.45152 10.6583 9.45152 10.3417 9.64678 10.1464Z'
        fill='currentColor'
      />
    </svg>
  );
};
