import React from 'react';

export const Command = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2.23223 2.23223C2.70107 1.76339 3.33696 1.5 4 1.5C4.66304 1.5 5.29893 1.76339 5.76777 2.23223C6.23661 2.70107 6.5 3.33696 6.5 4V5.5H9.5V4C9.5 3.33696 9.76339 2.70107 10.2322 2.23223C10.7011 1.76339 11.337 1.5 12 1.5C12.663 1.5 13.2989 1.76339 13.7678 2.23223C14.2366 2.70107 14.5 3.33696 14.5 4C14.5 4.66304 14.2366 5.29893 13.7678 5.76777C13.2989 6.23661 12.663 6.5 12 6.5H10.5V9.5H12C12.663 9.5 13.2989 9.76339 13.7678 10.2322C14.2366 10.7011 14.5 11.337 14.5 12C14.5 12.663 14.2366 13.2989 13.7678 13.7678C13.2989 14.2366 12.663 14.5 12 14.5C11.337 14.5 10.7011 14.2366 10.2322 13.7678C9.76339 13.2989 9.5 12.663 9.5 12V10.5H6.5V12C6.5 12.663 6.23661 13.2989 5.76777 13.7678C5.29893 14.2366 4.66304 14.5 4 14.5C3.33696 14.5 2.70107 14.2366 2.23223 13.7678C1.76339 13.2989 1.5 12.663 1.5 12C1.5 11.337 1.76339 10.7011 2.23223 10.2322C2.70107 9.76339 3.33696 9.5 4 9.5H5.5V6.5H4C3.33696 6.5 2.70107 6.23661 2.23223 5.76777C1.76339 5.29893 1.5 4.66304 1.5 4C1.5 3.33696 1.76339 2.70107 2.23223 2.23223ZM5.5 5.5V4C5.5 3.60218 5.34197 3.22064 5.06066 2.93934C4.77936 2.65804 4.39782 2.5 4 2.5C3.60218 2.5 3.22064 2.65804 2.93934 2.93934C2.65804 3.22064 2.5 3.60218 2.5 4C2.5 4.39782 2.65804 4.77936 2.93934 5.06066C3.22064 5.34197 3.60218 5.5 4 5.5H5.5ZM6.5 6.5V9.5H9.5V6.5H6.5ZM5.5 10.5H4C3.60218 10.5 3.22064 10.658 2.93934 10.9393C2.65804 11.2206 2.5 11.6022 2.5 12C2.5 12.3978 2.65804 12.7794 2.93934 13.0607C3.22064 13.342 3.60218 13.5 4 13.5C4.39783 13.5 4.77936 13.342 5.06066 13.0607C5.34196 12.7794 5.5 12.3978 5.5 12V10.5ZM10.5 10.5V12C10.5 12.3978 10.658 12.7794 10.9393 13.0607C11.2206 13.342 11.6022 13.5 12 13.5C12.3978 13.5 12.7794 13.342 13.0607 13.0607C13.342 12.7794 13.5 12.3978 13.5 12C13.5 11.6022 13.342 11.2206 13.0607 10.9393C12.7794 10.658 12.3978 10.5 12 10.5H10.5ZM10.5 5.5H12C12.3978 5.5 12.7794 5.34196 13.0607 5.06066C13.342 4.77936 13.5 4.39783 13.5 4C13.5 3.60218 13.342 3.22064 13.0607 2.93934C12.7794 2.65804 12.3978 2.5 12 2.5C11.6022 2.5 11.2206 2.65804 10.9393 2.93934C10.658 3.22064 10.5 3.60218 10.5 4V5.5Z'
        fill='currentColor'
      />
    </svg>
  );
};
