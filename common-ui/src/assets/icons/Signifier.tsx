import React from 'react';

export const Signifier = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='8' height='9' viewBox='0 0 8 9' fill='none' {...props}>
      <circle cx='4' cy='4.5' r='3.5' fill='currentColor' stroke='url(#paint0_linear_22549_12890)' />
      <defs>
        <linearGradient id='paint0_linear_22549_12890' x1='4' y1='0.5' x2='4' y2='8.5' gradientUnits='userSpaceOnUse'>
          <stop offset='0.4' stopColor='#1A1F29' stopOpacity='0.08' />
          <stop offset='1' stopColor='#1A1F29' stopOpacity='0.16' />
        </linearGradient>
      </defs>
    </svg>
  );
};
