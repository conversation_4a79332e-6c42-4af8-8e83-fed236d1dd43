export const Power = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='17' viewBox='0 0 16 17' fill='none' {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.00042 1.33203C8.27656 1.33203 8.50042 1.55589 8.50042 1.83203V8.4987C8.50042 8.77484 8.27656 8.9987 8.00042 8.9987C7.72428 8.9987 7.50042 8.77484 7.50042 8.4987V1.83203C7.50042 1.55589 7.72428 1.33203 8.00042 1.33203ZM4.10724 4.57171C4.30253 4.76694 4.30258 5.08352 4.10735 5.27882C3.33834 6.04808 2.8147 7.02809 2.60262 8.09493C2.39055 9.16178 2.49957 10.2676 2.91591 11.2724C3.33224 12.2773 4.03719 13.1362 4.94162 13.7405C5.84606 14.3447 6.90935 14.6673 7.99707 14.6673C9.08479 14.6673 10.1481 14.3447 11.0525 13.7405C11.957 13.1362 12.6619 12.2773 13.0782 11.2724C13.4946 10.2676 13.6036 9.16178 13.3915 8.09493C13.1795 7.02809 12.6558 6.04808 11.8868 5.27882C11.6916 5.08352 11.6916 4.76694 11.8869 4.57171C12.0822 4.37648 12.3988 4.37653 12.594 4.57183C13.5028 5.48095 14.1217 6.63914 14.3723 7.89996C14.623 9.16078 14.4941 10.4676 14.0021 11.6552C13.5101 12.8428 12.6769 13.8578 11.6081 14.572C10.5392 15.2861 9.28256 15.6673 7.99707 15.6673C6.71158 15.6673 5.45496 15.2861 4.38609 14.572C3.31721 13.8578 2.48409 12.8428 1.99206 11.6552C1.50003 10.4676 1.37118 9.16078 1.62181 7.89996C1.87245 6.63914 2.4913 5.48095 3.40013 4.57183C3.59536 4.37653 3.91194 4.37648 4.10724 4.57171Z'
        fill='currentColor'
      />
    </svg>
  );
};
