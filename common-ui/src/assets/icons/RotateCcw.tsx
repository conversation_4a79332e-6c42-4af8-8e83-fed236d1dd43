import React from 'react';

export const RotateCcw = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='17' viewBox='0 0 16 17' fill='none' {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.09378 2.07121C8.48928 1.87488 9.91079 2.13738 11.1441 2.81918C12.3775 3.50098 13.3558 4.56514 13.9317 5.85131C14.5077 7.13749 14.65 8.576 14.3373 9.95011C14.0246 11.3242 13.2738 12.5595 12.1981 13.4698C11.1223 14.3801 9.77978 14.9161 8.37287 14.9971C6.96595 15.0781 5.57083 14.6996 4.39771 13.9187C3.22459 13.1379 2.33702 11.9969 1.86874 10.6677C1.77698 10.4073 1.91373 10.1218 2.17418 10.03C2.43463 9.93825 2.72015 10.075 2.81191 10.3354C3.20815 11.4601 3.95917 12.4256 4.95181 13.0863C5.94445 13.747 7.12494 14.0673 8.31541 13.9987C9.50587 13.9302 10.6418 13.4767 11.5521 12.7064C12.4624 11.9362 13.0977 10.8909 13.3623 9.72822C13.6269 8.56551 13.5064 7.34831 13.0191 6.26001C12.5317 5.17171 11.7039 4.27127 10.6603 3.69436C9.61672 3.11745 8.41391 2.89533 7.2331 3.06146C6.0523 3.22759 4.95747 3.77298 4.11358 4.61545C4.11001 4.61901 4.10639 4.62252 4.10271 4.62597L1.92927 6.66824H4.66699C4.94313 6.66824 5.16699 6.8921 5.16699 7.16824C5.16699 7.44438 4.94313 7.66824 4.66699 7.66824H0.666992C0.39085 7.66824 0.166992 7.44438 0.166992 7.16824V3.16824C0.166992 2.8921 0.39085 2.66824 0.666992 2.66824C0.943135 2.66824 1.16699 2.8921 1.16699 3.16824V6.01231L3.41253 3.9023C4.40912 2.90975 5.70083 2.2672 7.09378 2.07121Z'
        fill='currentColor'
      />
    </svg>
  );
};
