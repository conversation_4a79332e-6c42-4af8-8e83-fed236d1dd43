import React from 'react';

export const UploadCloud = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='17' viewBox='0 0 16 17' fill='none' {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.78164 2.00396C6.66279 1.97149 7.53983 2.1392 8.34684 2.49448C9.15384 2.84977 9.8698 3.38337 10.4409 4.05518C10.9275 4.62756 11.2981 5.28757 11.5336 5.99836H12C12.8516 5.99892 13.6792 6.28309 14.3514 6.806C15.0236 7.32892 15.5026 8.06081 15.7127 8.88616C15.9227 9.7115 15.852 10.5833 15.5115 11.364C15.1711 12.1446 14.5804 12.7897 13.8326 13.1974C13.5902 13.3295 13.2865 13.2401 13.1543 12.9977C13.0221 12.7552 13.1115 12.4515 13.354 12.3194C13.9067 12.018 14.3433 11.5413 14.5949 10.9642C14.8465 10.3872 14.8988 9.74286 14.7436 9.13282C14.5883 8.52278 14.2343 7.98182 13.7374 7.59531C13.2406 7.20881 12.6291 6.99878 11.9996 6.99836H11.16C10.932 6.99836 10.7329 6.8442 10.6759 6.62351C10.493 5.91618 10.1522 5.25951 9.67898 4.70287C9.20579 4.14622 8.61257 3.70409 7.94391 3.40972C7.27525 3.11534 6.54856 2.97638 5.81846 3.00328C5.08837 3.03018 4.37387 3.22224 3.72868 3.56502C3.0835 3.9078 2.52442 4.39239 2.09347 4.98235C1.66253 5.5723 1.37093 6.25228 1.24061 6.97115C1.11028 7.69002 1.14462 8.42908 1.34104 9.13278C1.53746 9.83647 1.89085 10.4865 2.37464 11.0339C2.5575 11.2409 2.53799 11.5568 2.33107 11.7397C2.12415 11.9226 1.80817 11.903 1.62531 11.6961C1.04142 11.0354 0.614916 10.2509 0.377858 9.40163C0.1408 8.55234 0.0993575 7.66037 0.256646 6.79277C0.413935 5.92516 0.765862 5.1045 1.28597 4.39249C1.80608 3.68047 2.48083 3.09563 3.2595 2.68192C4.03817 2.26822 4.90049 2.03642 5.78164 2.00396ZM7.99998 7.99837C8.13259 7.99837 8.25977 8.05105 8.35354 8.14482L11.0202 10.8115C11.2155 11.0067 11.2155 11.3233 11.0202 11.5186C10.8249 11.7139 10.5084 11.7139 10.3131 11.5186L8.49998 9.70548L8.49997 14.4984C8.49997 14.7745 8.27612 14.9984 7.99997 14.9984C7.72383 14.9984 7.49997 14.7745 7.49997 14.4984L7.49998 9.70548L5.68687 11.5186C5.49161 11.7139 5.17503 11.7139 4.97976 11.5186C4.7845 11.3233 4.7845 11.0067 4.97976 10.8115L7.64643 8.14482C7.7402 8.05105 7.86738 7.99837 7.99998 7.99837Z'
        fill='currentColor'
      />
    </svg>
  );
};
