import React from 'react';

export const Layers = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none' {...props}>
      <path
        d='M4.14935 8.0696C2.49445 7.40764 1.66699 7.07666 1.66699 6.66536C1.66699 6.25407 2.49445 5.92309 4.14935 5.26113L6.48974 4.32497C8.14465 3.66301 8.9721 3.33203 10.0003 3.33203C11.0286 3.33203 11.856 3.66301 13.5109 4.32497L15.8513 5.26113C17.5062 5.92309 18.3337 6.25407 18.3337 6.66536C18.3337 7.07666 17.5062 7.40764 15.8513 8.0696L13.5109 9.00575C11.856 9.66772 11.0286 9.9987 10.0003 9.9987C8.9721 9.9987 8.14465 9.66772 6.48974 9.00575L4.14935 8.0696Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M1.66699 6.66536C1.66699 7.07666 2.49445 7.40764 4.14935 8.0696L6.48974 9.00575C8.14465 9.66772 8.9721 9.9987 10.0003 9.9987C11.0286 9.9987 11.856 9.66772 13.5109 9.00575L15.8513 8.0696C17.5062 7.40764 18.3337 7.07666 18.3337 6.66536C18.3337 6.25407 17.5062 5.92309 15.8513 5.26113L13.5109 4.32497C11.856 3.66301 11.0286 3.33203 10.0003 3.33203C8.9721 3.33203 8.14465 3.66301 6.48974 4.32497L4.14935 5.26113C2.49445 5.92309 1.66699 6.25407 1.66699 6.66536Z'
        fill='currentColor'
      />
      <path
        opacity='0.7'
        d='M4.80543 8.33203L4.14935 8.59446C2.49445 9.25643 1.66699 9.58741 1.66699 9.9987C1.66699 10.41 2.49445 10.741 4.14935 11.4029L6.48974 12.3391C8.14465 13.001 8.9721 13.332 10.0003 13.332C11.0286 13.332 11.856 13.001 13.5109 12.3391L15.8513 11.4029C17.5062 10.741 18.3337 10.41 18.3337 9.9987C18.3337 9.58741 17.5062 9.25643 15.8513 8.59446L15.1952 8.33203L13.5109 9.00575C11.856 9.66772 11.0286 9.9987 10.0003 9.9987C8.9721 9.9987 8.14465 9.66772 6.48974 9.00575L4.80543 8.33203Z'
        fill='currentColor'
      />
      <path
        opacity='0.4'
        d='M4.80543 11.668L4.14935 11.9304C2.49445 12.5924 1.66699 12.9233 1.66699 13.3346C1.66699 13.7459 2.49445 14.0769 4.14935 14.7389L6.48974 15.675C8.14465 16.337 8.9721 16.668 10.0003 16.668C11.0286 16.668 11.856 16.337 13.5109 15.675L15.8513 14.7389C17.5062 14.0769 18.3337 13.7459 18.3337 13.3346C18.3337 12.9233 17.5062 12.5924 15.8513 11.9304L15.1952 11.668L13.5109 12.3417C11.856 13.0037 11.0286 13.3346 10.0003 13.3346C8.9721 13.3346 8.14465 13.0037 6.48974 12.3417L4.80543 11.668Z'
        fill='currentColor'
      />
    </svg>
  );
};
