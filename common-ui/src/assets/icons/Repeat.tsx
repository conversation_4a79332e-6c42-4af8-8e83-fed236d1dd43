import { SVGProps } from 'react';

export const Repeat = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.9798 0.314415C11.1751 0.119153 11.4916 0.119153 11.6869 0.314415L14.3536 2.98108C14.4473 3.07485 14.5 3.20203 14.5 3.33464C14.5 3.46724 14.4473 3.59442 14.3536 3.68819L11.6869 6.35485C11.4916 6.55012 11.1751 6.55012 10.9798 6.35485C10.7845 6.15959 10.7845 5.84301 10.9798 5.64775L12.7929 3.83464L4.66667 3.83466C4.09203 3.83466 3.54093 4.06293 3.1346 4.46926C2.72827 4.87559 2.5 5.42669 2.5 6.00132V7.33466C2.5 7.6108 2.27614 7.83466 2 7.83466C1.72386 7.83466 1.5 7.6108 1.5 7.33466V6.00132C1.5 5.16147 1.83363 4.35602 2.4275 3.76215C3.02136 3.16829 3.82681 2.83466 4.66667 2.83466L12.7929 2.83464L10.9798 1.02152C10.7845 0.82626 10.7845 0.509678 10.9798 0.314415ZM14 8.16797C14.2761 8.16797 14.5 8.39183 14.5 8.66797V10.0013C14.5 10.8412 14.1664 11.6466 13.5725 12.2405C12.9786 12.8343 12.1732 13.168 11.3333 13.168L3.20711 13.168L5.02022 14.9811C5.21548 15.1764 5.21548 15.4929 5.02022 15.6882C4.82496 15.8835 4.50838 15.8835 4.31311 15.6882L1.64645 13.0215C1.45118 12.8263 1.45118 12.5097 1.64645 12.3144L4.31311 9.64776C4.50838 9.4525 4.82496 9.4525 5.02022 9.64776C5.21548 9.84302 5.21548 10.1596 5.02022 10.3549L3.20711 12.168L11.3333 12.168C11.908 12.168 12.4591 11.9397 12.8654 11.5334C13.2717 11.127 13.5 10.5759 13.5 10.0013V8.66797C13.5 8.39183 13.7239 8.16797 14 8.16797Z'
        fill='currentColor'
      />
    </svg>
  );
};
