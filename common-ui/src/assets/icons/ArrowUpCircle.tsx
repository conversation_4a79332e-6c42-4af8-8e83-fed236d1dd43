import React from 'react';

export const ArrowUpCircle = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='17' viewBox='0 0 16 17' fill='none' {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.99967 2.33203C4.59392 2.33203 1.83301 5.09294 1.83301 8.4987C1.83301 11.9045 4.59392 14.6654 7.99967 14.6654C11.4054 14.6654 14.1663 11.9045 14.1663 8.4987C14.1663 5.09294 11.4054 2.33203 7.99967 2.33203ZM0.833008 8.4987C0.833008 4.54066 4.04163 1.33203 7.99967 1.33203C11.9577 1.33203 15.1663 4.54066 15.1663 8.4987C15.1663 12.4567 11.9577 15.6654 7.99967 15.6654C4.04163 15.6654 0.833008 12.4567 0.833008 8.4987ZM7.99967 5.33203C8.13228 5.33203 8.25946 5.38471 8.35323 5.47848L11.0199 8.14514C11.2152 8.34041 11.2152 8.65699 11.0199 8.85225C10.8246 9.04751 10.508 9.04751 10.3128 8.85225L8.49967 7.03913L8.49966 11.1654C8.49966 11.4415 8.2758 11.6654 7.99966 11.6654C7.72352 11.6654 7.49966 11.4415 7.49966 11.1654L7.49967 7.03914L5.68656 8.85225C5.4913 9.04751 5.17472 9.04751 4.97945 8.85225C4.78419 8.65699 4.78419 8.34041 4.97945 8.14514L7.64612 5.47848C7.73989 5.38471 7.86706 5.33203 7.99967 5.33203Z'
        fill='currentColor'
      />
    </svg>
  );
};
