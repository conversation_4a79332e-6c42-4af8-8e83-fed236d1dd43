services:
  main:
    image: arm64v8/node:20.15-alpine3.20
    container_name: ${PROJECT}_main
    user: "node"
    working_dir: /home/<USER>
    env_file:
      - .env
    environment:
      NODE_ENV: ${ENVIRONMENT}
      PORT: ${DOCKER_PORT}
      PUBLIC_PORT: ${MAIN_PORT}
      DOMAIN: ${DOMAIN}
      VERSION: '1.0.0'
    command: sh -c "npm i && npm start"
    # command: tail -f /dev/null
    ports:
      - ${MAIN_PORT}:${DOCKER_PORT}
    volumes:
      - './main:/home/<USER>'
      - './remotesConfig.development.js:/home/<USER>/src/remotesConfig.js'
      - './.eslintrc.js:/home/<USER>/.eslintrc.js'
      - './.eslintignore:/home/<USER>/.eslintignore'
      - './eslint-custom-plugins:/home/<USER>/eslint-custom-plugins'
      - './global.css:/home/<USER>/src/global.css'
      - './tailwind.config.js:/home/<USER>/tailwind.config.js'

  ui:
    image: arm64v8/node:20.15-alpine3.20
    container_name: ${PROJECT}_ui
    user: "node"
    working_dir: /home/<USER>
    env_file:
      - .env
    environment:
      NODE_ENV: ${ENVIRONMENT}
      PORT: ${DOCKER_PORT}
      PUBLIC_PORT: ${UI_PORT}
    command: sh -c "npm i && npm start"
    # command: tail -f /dev/null
    ports:
      - ${UI_PORT}:${DOCKER_PORT}
    volumes:
      - './common-ui:/home/<USER>'
      - './remotesConfig.development.js:/home/<USER>/src/remotesConfig.js'
      - './.eslintrc.js:/home/<USER>/.eslintrc.js'
      - './.eslintignore:/home/<USER>/.eslintignore'
      - './eslint-custom-plugins:/home/<USER>/eslint-custom-plugins'
      - './global.css:/home/<USER>/src/global.css'
      - './tailwind.config.js:/home/<USER>/tailwind.config.js'

  services:
    image: arm64v8/node:20.15-alpine3.20
    container_name: ${PROJECT}_services
    user: "node"
    working_dir: /home/<USER>
    env_file:
      - .env
    environment:
      NODE_ENV: ${ENVIRONMENT}
      PORT: ${DOCKER_PORT}
      PUBLIC_PORT: ${SERVICES_PORT}
    command: sh -c "npm i && npm start"
    # command: tail -f /dev/null
    ports:
      - ${SERVICES_PORT}:${DOCKER_PORT}
    volumes:
      - './services:/home/<USER>'
      - './remotesConfig.development.js:/home/<USER>/src/remotesConfig.js'
      - './.eslintrc.js:/home/<USER>/.eslintrc.js'
      - './.eslintignore:/home/<USER>/.eslintignore'
      - './eslint-custom-plugins:/home/<USER>/eslint-custom-plugins'

  customer:
    image: arm64v8/node:20.15-alpine3.20
    container_name: ${PROJECT}_customer
    user: "node"
    working_dir: /home/<USER>
    env_file:
      - .env
    environment:
      NODE_ENV: ${ENVIRONMENT}
      PORT: ${DOCKER_PORT}
      PUBLIC_PORT: ${CUSTOMER_PORT}
    command: sh -c "npm i && npm start"
    # command: tail -f /dev/null
    ports:
      - ${CUSTOMER_PORT}:${DOCKER_PORT}
    volumes:
      - './apps/csa:/home/<USER>'
      - './services/src/types:/home/<USER>/src/serviceTypes'
      - './common-ui/src/types:/home/<USER>/src/uiTypes'
      - './remotesConfig.development.js:/home/<USER>/src/remotesConfig.js'
      - './.eslintrc.js:/home/<USER>/.eslintrc.js'
      - './.eslintignore:/home/<USER>/.eslintignore'
      - './eslint-custom-plugins:/home/<USER>/eslint-custom-plugins'
      - './global.css:/home/<USER>/src/global.css'
      - './tailwind.config.js:/home/<USER>/tailwind.config.js'

  reporting:
    image: arm64v8/node:20.15-alpine3.20
    container_name: ${PROJECT}_reporting
    user: "node"
    working_dir: /home/<USER>
    env_file:
      - .env
    environment:
      NODE_ENV: ${ENVIRONMENT}
      PORT: ${DOCKER_PORT}
      PUBLIC_PORT: ${REPORTING_PORT}
    command: sh -c "npm i && npm start"
    # command: tail -f /dev/null
    ports:
      - ${REPORTING_PORT}:${DOCKER_PORT}
    volumes:
      - './apps/reporting:/home/<USER>'
      - './services/src/types:/home/<USER>/src/serviceTypes'
      - './common-ui/src/types:/home/<USER>/src/uiTypes'
      - './remotesConfig.development.js:/home/<USER>/src/remotesConfig.js'
      - './.eslintrc.js:/home/<USER>/.eslintrc.js'
      - './.eslintignore:/home/<USER>/.eslintignore'
      - './eslint-custom-plugins:/home/<USER>/eslint-custom-plugins'
      - './global.css:/home/<USER>/src/global.css'
      - './tailwind.config.js:/home/<USER>/tailwind.config.js'

  network:
    image: arm64v8/node:20.15-alpine3.20
    container_name: ${PROJECT}_network
    user: "node"
    working_dir: /home/<USER>
    env_file:
      - .env
    environment:
      NODE_ENV: ${ENVIRONMENT}
      PORT: ${DOCKER_PORT}
      PUBLIC_PORT: ${NETWORK_PORT}
    command: sh -c "npm i && npm start"
    # command: tail -f /dev/null
    ports:
      - ${NETWORK_PORT}:${DOCKER_PORT}
    volumes:
      - './apps/nsm:/home/<USER>'
      - './services/src/types:/home/<USER>/src/serviceTypes'
      - './common-ui/src/types:/home/<USER>/src/uiTypes'
      - './remotesConfig.development.js:/home/<USER>/src/remotesConfig.js'
      - './.eslintrc.js:/home/<USER>/.eslintrc.js'
      - './.eslintignore:/home/<USER>/.eslintignore'
      - './eslint-custom-plugins:/home/<USER>/eslint-custom-plugins'
      - './global.css:/home/<USER>/src/global.css'
      - './tailwind.config.js:/home/<USER>/tailwind.config.js'

  storybook:
    image: arm64v8/node:20.15-alpine3.20
    container_name: ${PROJECT}_storybook
    user: "node"
    working_dir: /home/<USER>
    env_file:
      - .env
    environment:
      NODE_ENV: ${ENVIRONMENT}
      PORT: ${DOCKER_PORT}
      PUBLIC_PORT: ${STORYBOOK_PORT}
    command: sh -c "npm i && npm start"
    # command: tail -f /dev/null
    ports:
      - ${STORYBOOK_PORT}:6006
    volumes:
      - './storybook:/home/<USER>'
      - './remotesConfig.development.js:/home/<USER>/src/remotesConfig.js'

  admincp:
    image: arm64v8/node:20.15-alpine3.20
    container_name: ${PROJECT}_admincp
    user: "node"
    working_dir: /home/<USER>
    env_file:
      - .env
    environment:
      NODE_ENV: ${ENVIRONMENT}
      PORT: ${DOCKER_PORT}
      PUBLIC_PORT: ${ADMINCP_PORT}
      DOMAIN: ${DOMAIN}
    command: sh -c "npm i && npm start"
    ports:
      - ${ADMINCP_PORT}:${DOCKER_PORT}
    volumes:
      - ./apps/admincp:/home/<USER>
      - './services/src/types:/home/<USER>/src/serviceTypes'
      - './common-ui/src/types:/home/<USER>/src/uiTypes'
      - ./remotesConfig.development.js:/home/<USER>/src/remotesConfig.js
      - ./.eslintrc.js:/home/<USER>/.eslintrc.js
      - ./.eslintignore:/home/<USER>/.eslintignore
      - ./eslint-custom-plugins:/home/<USER>/eslint-custom-plugins
      - ./global.css:/home/<USER>/src/global.css
      - ./tailwind.config.js:/home/<USER>/tailwind.config.js

  batchfw:
    image: arm64v8/node:20.15-alpine3.20
    container_name: ${PROJECT}_batchfw
    user: "node"
    working_dir: /home/<USER>
    env_file:
      - .env
    environment:
      NODE_ENV: ${ENVIRONMENT}
      PORT: ${DOCKER_PORT}
      PUBLIC_PORT: ${BATCHFW_PORT}
      DOMAIN: ${DOMAIN}
    command: sh -c "npm i && npm start"
    ports:
      - ${BATCHFW_PORT}:${DOCKER_PORT}
    volumes:
      - ./apps/batchfw:/home/<USER>
      - './services/src/types:/home/<USER>/src/serviceTypes'
      - './common-ui/src/types:/home/<USER>/src/uiTypes'
      - ./remotesConfig.development.js:/home/<USER>/src/remotesConfig.js
      - ./.eslintrc.js:/home/<USER>/.eslintrc.js
      - ./.eslintignore:/home/<USER>/.eslintignore
      - ./eslint-custom-plugins:/home/<USER>/eslint-custom-plugins
      - ./global.css:/home/<USER>/src/global.css
      - ./tailwind.config.js:/home/<USER>/tailwind.config.js

  techtoolbox:
    image: arm64v8/node:20.15-alpine3.20
    container_name: ${PROJECT}_techToolbox
    user: "node"
    working_dir: /home/<USER>
    env_file:
      - .env
    environment:
      NODE_ENV: ${ENVIRONMENT}
      PORT: ${DOCKER_PORT}
      PUBLIC_PORT: ${TECHTOOLBOX_PORT}
    command: sh -c "npm i && npm start"
    ports:
      - ${TECHTOOLBOX_PORT}:${DOCKER_PORT}
    volumes:
      - './apps/techtoolbox:/home/<USER>'
      - './services/src/types:/home/<USER>/src/serviceTypes'
      - './common-ui/src/types:/home/<USER>/src/uiTypes'
      - './remotesConfig.development.js:/home/<USER>/src/remotesConfig.js'
      - './.eslintrc.js:/home/<USER>/.eslintrc.js'
      - './.eslintignore:/home/<USER>/.eslintignore'
      - './eslint-custom-plugins:/home/<USER>/eslint-custom-plugins'
      - './global.css:/home/<USER>/src/global.css'
      - './tailwind.config.js:/home/<USER>/tailwind.config.js'
