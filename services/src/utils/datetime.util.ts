import dayjs, { Dayjs, ManipulateType } from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import relativeTime from 'dayjs/plugin/relativeTime';
import updateLocale from 'dayjs/plugin/updateLocale';
import utc from 'dayjs/plugin/utc';

dayjs.extend(updateLocale);
dayjs.extend(utc);
dayjs.extend(isSameOrBefore);

const thresholds = [
  { l: 's', r: 1 },
  { l: 'm', r: 1 },
  { l: 'mm', r: 59, d: 'minute' },
  { l: 'h', r: 1 },
  { l: 'hh', r: 23, d: 'hour' },
  { l: 'd', r: 1 },
  { l: 'dd', r: 99999, d: 'day' },
];

dayjs.extend(relativeTime, {
  thresholds,
});

dayjs.updateLocale('en', {
  relativeTime: {
    future: 'in %s',
    past: '%s ago',
    s: 'a few seconds',
    m: '1 minute',
    mm: '%d minutes',
    h: '1 hour',
    hh: '%d hours',
    d: '1 day',
    dd: '%d days',
    M: '1 month',
    MM: '%d months',
    y: '1 year',
    yy: '%d years',
  },
});

type TDate = Date | string | number | null | undefined;

/**
 * Get the unix time from a date in miliseconds
 * @param date - The date to get the unix time from
 * @returns The unix time
 */
export const getUnixTime = (date: TDate) => {
  try {
    return dayjs(date).valueOf();
  } catch (error) {
    return null;
  }
};

export const getDateFromUnixTime = (unixTime: number) => {
  try {
    if (!unixTime) {
      return null;
    }
    const isUnixTimeMilliSeconds = unixTime.toString().length > 10;
    return isUnixTimeMilliSeconds ? dayjs(unixTime) : dayjs.unix(unixTime);
  } catch (error) {
    return null;
  }
};

/**
 * https://day.js.org/docs/en/plugin/relative-time
 * Get the relative time from now
 * @param unixTime - The time to get the relative time from
 * @param withoutSuffix - Whether to show the suffix like: 'ago' or 'in'...
 * @returns The relative time from now
 * In days, following old GUI from CC, eg: 43 days ago, 365 days ago,...
 */
export const getRelativeTimeFromNow = (unixTime: number, withoutSuffix?: boolean) => {
  try {
    const time = getDateFromUnixTime(unixTime);
    if (time === null) {
      return null;
    }
    return time.fromNow(withoutSuffix);
  } catch (error) {
    return null;
  }
};

/**
 * https://day.js.org/docs/en/plugin/relative-time
 * Get the relative time to now
 * @param unixTime - The unix time to get the relative time to
 * @param withoutSuffix - Whether to show the suffix
 * @returns The relative time to now
 */
export const getRelativeTimeToNow = (unixTime: number, withoutSuffix?: boolean) => {
  try {
    const time = getDateFromUnixTime(unixTime);
    if (time === null) {
      return null;
    }
    return time.toNow(withoutSuffix);
  } catch (error) {
    return null;
  }
};

/**
 * Subtract a number of days from a date
 * Since days get from BFF is unix time, days will always be a number.
 * @param days - The date to subtract from in unix time with seconds.
 * @param amount - The number of days to subtract
 * @param unit - The unit of time to subtract from
 * @returns The date after subtracting the number of days
 */
export const subtract = (days: number, amount: number, unit?: ManipulateType) => {
  try {
    const date = getDateFromUnixTime(days);
    if (date === null) {
      return null;
    }
    return date.subtract(amount, unit);
  } catch (error) {
    return null;
  }
};

/**
 * Check if the unix time is today
 * @param unixTime - The unix time to check in seconds
 * @returns Whether the unix time is today
 */
export const isToday = (unixTime: number) => {
  try {
    const date = getDateFromUnixTime(unixTime);
    if (date === null) {
      return false;
    }
    return date.isSame(dayjs(), 'day');
  } catch (error) {
    return false;
  }
};

/**
 * Format the date
 * @param unixTime - The unix time to format in seconds
 * @param format - The format to format the date
 * @returns The formatted date
 */
export const formatDate = (unixTime: number, format: string, isUtc = false) => {
  try {
    const date = getDateFromUnixTime(unixTime);
    if (date === null) {
      return null;
    }
    return isUtc ? date.utc().format(format) : date.format(format);
  } catch (error) {
    return null;
  }
};

/**
 * Get the date from today
 * @param timeRange - The time range to get the date from
 * @param unit - The unit of time to get the date from
 * @param atMidnight - Whether to get the date at midnight.
 * @returns The date from today
 */
export const getDateFromToday = (timeRange: number, defaultUnit?: ManipulateType, atMidnight = false): Dayjs | null => {
  try {
    const unit = defaultUnit || 'day';
    let date = dayjs().subtract(timeRange, unit);
    if (atMidnight) {
      // start of the day.
      date = date.startOf('day'); // we get the date from today, not following the unit
    }
    return date;
  } catch (error) {
    return null;
  }
};

/**
 * Get the day from today by weeks
 * @param week - The week to get the day from. Date format should be like '1W', '2W', '3W', '4W'
 * @returns The day from today by weeks
 */
export const getDayFromTodayByWeeks = (week: string): Dayjs | null => {
  try {
    const weeks = Number(week.replace('W', ''));
    return getDateFromToday(weeks, 'week');
  } catch (error) {
    return null;
  }
};

export const getDayFromTodayByDays = (day: string): Dayjs | null => {
  try {
    const days = Number(day.replace('D', ''));
    return getDateFromToday(days, 'day');
  } catch (error) {
    return null;
  }
};

export const getDayjsFromDate = (date: TDate): Dayjs => {
  return dayjs(date);
};

export enum DATETIME_FORMAT {
  DATE = 'MMM DD, YYYY',
  DATE_TIME = 'MMM DD, YYYY HH:mm',
  DATE_TIME_CSV = 'YYYY-MMM-DD HH:mm',
  TIME_HOUR = 'HH:mm',
  MONTH_DAY = 'MMM DD',
}
