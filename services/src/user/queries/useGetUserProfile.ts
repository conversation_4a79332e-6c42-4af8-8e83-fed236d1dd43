import { AxonQueryOptions, BFFResponseDTO } from './../../types/index';
import { useQuery } from '@tanstack/react-query';
import { getUserProfile } from '../apis/getUserProfile';
import { queryKeys } from '../constants/queryKeys';
import { UserProfileResponse } from '../types/user.type';

export const useGetUserProfile = (option?: AxonQueryOptions<BFFResponseDTO<UserProfileResponse>>) => {
  return useQuery({
    queryKey: queryKeys.getUserProfile(),
    queryFn: () => getUserProfile(),
    ...option,
  });
};
