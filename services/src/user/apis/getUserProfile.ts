import api from '@/api/apiService';
import { ENDPOINTS } from '../constants/endpoints';
import { UserProfileResponse } from '../types/user.type';
import { BFFResponseDTO } from './../../types/index';

export async function getUserProfile(): Promise<BFFResponseDTO<UserProfileResponse>> {
  const response = await api.get<BFFResponseDTO<UserProfileResponse>>(`${ENDPOINTS.GET_USER_PROFILE}`);

  return response.data;
}
