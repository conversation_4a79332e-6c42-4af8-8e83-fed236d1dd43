export interface CpeHistoryResponse {
  datapoints: TCpeHistoryDataPoint[];
}

export type TCpeHistoryDataPoint = {
  date: number;
  cpuUsage?: number | null;
  cpuLoad?: number | null;
  freeMemory?: number | null;
  chipsetTemperature?: number | null;
  powerCycle?: number | null;
  qoe?: number | null;
  rssi?: {
    [key: string]: number | null;
  };
  disconnEvents?: {
    [key: string]: number | null;
  };
  reconnectEvents?: {
    [key: string]: number | null;
  };
};

export interface CpeHistoryRequestParams {
  startDate: number;
  endDate?: number;
  deviceId: string;
  customerId: string;
}
