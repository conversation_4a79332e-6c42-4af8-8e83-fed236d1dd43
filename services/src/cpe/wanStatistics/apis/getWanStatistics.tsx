import api from '@/api/apiService';
import { BFFResponseDTO } from '@/types';
import { ENDPOINTS } from '../constants/endpoints';
import { IWanStatisticsQuery, IWanStatisticResponseData } from '../types';

export const getWanStatistics = async ({
  deviceId,
  customerId,
  startDate,
  endDate,
}: IWanStatisticsQuery): Promise<BFFResponseDTO<IWanStatisticResponseData>> => {
  const res = await api.get(ENDPOINTS.WAN_STATISTICS, {
    params: {
      deviceId,
      customerId,
      startDate,
      endDate,
    },
  });
  return res.data;
};
