import api from '@/api/apiService';
import { BFFResponseDTO } from '@/types';
import { ENDPOINTS } from '../constants/endpoints';
import { IWanStatisticDrawerResponseData, IWanStatisticsQuery } from '../types';

export const getWanStatisticsDrawer = async ({
  deviceId,
  customerId,
  startDate,
  endDate,
}: IWanStatisticsQuery): Promise<BFFResponseDTO<IWanStatisticDrawerResponseData>> => {
  const res = await api.get(ENDPOINTS.WAN_STATISTICS_DRAWER, {
    params: {
      deviceId,
      customerId,
      startDate,
      endDate,
    },
  });
  return res.data;
};
