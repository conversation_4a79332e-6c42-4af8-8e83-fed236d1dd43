import { useQuery } from '@tanstack/react-query';
import { AxonQueryOptions, BFFResponseDTO } from '../../../types';
import { getWanStatistics } from '../apis/getWanStatistics';
import { QUERY_KEYS } from '../constants/queryKeys';
import { IWanStatisticResponseData, IWanStatisticsQuery } from '../types';

export const useGetWanStatistics = (
  { deviceId, customerId, startDate, endDate }: IWanStatisticsQuery,
  options?: AxonQueryOptions<BFFResponseDTO<IWanStatisticResponseData>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.WAN_STATISTICS(deviceId),
    queryFn: () => getWanStatistics({ deviceId, customerId, startDate, endDate }),
    ...options,
  });
};
