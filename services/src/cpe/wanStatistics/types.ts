export interface IWanStatistics {
  wanId: string;
  wanType: string;
  wanDown: string | number | null;
  wanDownIssue: string | null;
  wanUp: string | number | null;
  wanUpIssue: string | null;
  wanDescription: string | null;
  wanUnit: string;
}

export interface IWanDetail {
  wanId: string;
  wanType: string;
  wanMinValue: string | number | null;
  wanMaxValue: string | number | null;
  wanAverage: string | number | null;
  wanValue: string | number | null;
  noOfTest: number | null;
  wanUnit: string;
}

export interface IWanStatisticResponseData {
  results: IWanStatistics[];
  status?: number;
  lastUpdated?: number;
}

export interface IWanStatisticDrawerResponseData {
  results: IWanDetail[];
}

export interface IWanStatisticsQuery {
  deviceId: string;
  customerId: string;
  startDate: number;
  endDate?: number;
}
