import {
  AxonButton,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuItem,
  AxonDropdownMenuLabel,
  AxonDropdownMenuSeparator,
  AxonDropdownMenuTrigger,
} from 'ui/UIComponents';

import useTabStore from '@/stores/tab.store';
import { useState } from 'react';
import {
  ArrowUpCircle,
  Command,
  Download,
  DownloadCloud,
  Power,
  RotateCcw,
  Settings,
  Shuffle,
  UploadCloud,
} from 'ui/UIAssets';
import DeviceRebootDialog from './MenuItemDialogs/DeviceRebootDialog';
import EnvironmenRebootDialog from './MenuItemDialogs/EnvironmenRebootDialog';
import FactoryResetDialog from './MenuItemDialogs/FactoryResetDialog';
import OptimizeWifiChannelsDialog from './MenuItemDialogs/OptimizeWifiChannelsDialog';
import RestartPppDialog from './MenuItemDialogs/RestartPppDialog';
import RestartWanInterfaceDialog from './MenuItemDialogs/RestartWanInterfaceDialog';
import Tr069ProcessRestart from './MenuItemDialogs/Tr069ProcessRestartDialog';
import PushPppConfigDialog from './MenuItemDialogs/PushPppConfigDialog';
import FirmwareUpdateDialog from './MenuItemDialogs/FirmwareUpdateDialog';
import RestartWifiInterfaceDialog from './MenuItemDialogs/RestartWifiInterfaceDialog';

const ActionMenu = () => {
  const deviceId = useTabStore((state) => state.activeTab?.deviceId);
  const [dialogsState, setDialogsState] = useState({
    // CPE
    deviceRebootDialog: false,
    environmentRebootDialog: false,
    factoryResetDialog: false,
    tr069ProcessRestartDialog: false,
    firmwareUpdateDialog: false,

    // WIFI
    wifiConfigurationDialog: false,
    optimizeWifiChannelsDialog: false,
    restartWifiInterfaceDialog: false,

    // WAN
    restartWanInterfaceDialog: false,
    restartPppDialog: false,
    pushPppConfigDialog: false,
  });

  return (
    <>
      <AxonDropdownMenu>
        <AxonDropdownMenuTrigger asChild>
          <AxonButton variant='primary'>
            <Command className='mr-2 size-4' />
            Action
          </AxonButton>
        </AxonDropdownMenuTrigger>
        <AxonDropdownMenuContent className='w-2xs'>
          <AxonDropdownMenuLabel className='text-content-tertiary'>CPE</AxonDropdownMenuLabel>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, deviceRebootDialog: true })}>
            <Power className='size-4' /> Device reboot
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, environmentRebootDialog: true })}>
            <Power className='size-4' />
            Environment reboot
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <Settings className='size-4' />
            Auto-reboot config
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, factoryResetDialog: true })}>
            <RotateCcw className='size-4' />
            Factory Reset
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <RotateCcw className='size-4' />
            Parental control reset
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, tr069ProcessRestartDialog: true })}>
            <RotateCcw className='size-4' />
            TR069 process restart
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, firmwareUpdateDialog: true })}>
            <ArrowUpCircle className='size-4' />
            Firmware update
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <DownloadCloud className='size-4' />
            CPE config backup
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <UploadCloud className='size-4' />
            CPE config restore
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <Download className='size-4' />
            Download device log
          </AxonDropdownMenuItem>
          <AxonDropdownMenuSeparator />

          <AxonDropdownMenuLabel className='text-content-tertiary'>WIFI</AxonDropdownMenuLabel>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, wifiConfigurationDialog: true })}>
            <Settings className='size-4' />
            WI-FI configuration
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, optimizeWifiChannelsDialog: true })}>
            <Shuffle className='size-4' />
            Optimize Wi-Fi channels
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <RotateCcw className='size-4' />
            Restart Wi-Fi driver
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, restartWifiInterfaceDialog: true })}>
            <RotateCcw className='size-4' />
            Restart Wi-Fi interface
          </AxonDropdownMenuItem>

          <AxonDropdownMenuSeparator />
          <AxonDropdownMenuLabel className='text-content-tertiary'>WAN</AxonDropdownMenuLabel>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, restartWanInterfaceDialog: true })}>
            <RotateCcw className='size-4' />
            Restart WAN interface
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, restartPppDialog: true })}>
            <RotateCcw className='size-4' />
            Restart PPP
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, pushPppConfigDialog: true })}>
            <ArrowUpCircle className='size-4' />
            Push PPP config
          </AxonDropdownMenuItem>
        </AxonDropdownMenuContent>
      </AxonDropdownMenu>
      {/* CPE */}
      {deviceId && (
        <DeviceRebootDialog
          open={dialogsState.deviceRebootDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, deviceRebootDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <EnvironmenRebootDialog
          open={dialogsState.environmentRebootDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, environmentRebootDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <FactoryResetDialog
          open={dialogsState.factoryResetDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, factoryResetDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <Tr069ProcessRestart
          open={dialogsState.tr069ProcessRestartDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, tr069ProcessRestartDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <FirmwareUpdateDialog
          open={dialogsState.firmwareUpdateDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, firmwareUpdateDialog: open })}
          deviceId={deviceId}
        />
      )}

      {/* WIFI */}
      {deviceId && (
        <OptimizeWifiChannelsDialog
          open={dialogsState.optimizeWifiChannelsDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, optimizeWifiChannelsDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <RestartWifiInterfaceDialog
          open={dialogsState.restartWifiInterfaceDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, restartWifiInterfaceDialog: open })}
          deviceId={deviceId}
        />
      )}

      {/* WAN */}
      {deviceId && (
        <RestartWanInterfaceDialog
          open={dialogsState.restartWanInterfaceDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, restartWanInterfaceDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <RestartPppDialog
          open={dialogsState.restartPppDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, restartPppDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <PushPppConfigDialog
          open={dialogsState.pushPppConfigDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, pushPppConfigDialog: open })}
          deviceId={deviceId}
        />
      )}
    </>
  );
};

export default ActionMenu;
