import { useState } from 'react';
import {
  AxonAlertDialog,
  AxonAlertDialogAction,
  AxonAlertDialogCancel,
  AxonAlertDialogContent,
  AxonAlertDialogDescription,
  AxonAlertDialogFooter,
  AxonAlertDialogHeader,
  AxonAlertDialogTitle,
  AxonInput,
  AxonLabel,
  AxonSelect,
  AxonSelectContent,
  AxonSelectItem,
  AxonSelectTrigger,
  AxonSelectValue,
} from 'ui/UIComponents';

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceId: string;
};

const RestartWifiInterfaceDialog = ({ deviceId, onOpenChange, open }: Props) => {
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

  const [selectedBand, setSelectedBand] = useState('');
  const wifibands = ['2.4 GHz', '5 GHz', '6 GHz'];

  return (
    <>
      <AxonAlertDialog open={open} onOpenChange={onOpenChange} key={deviceId}>
        <AxonAlertDialogContent className='min-w-[350px] gap-0'>
          <AxonAlertDialogHeader>
            <AxonAlertDialogTitle></AxonAlertDialogTitle>
            <AxonAlertDialogDescription></AxonAlertDialogDescription>
          </AxonAlertDialogHeader>
          <div className='flex flex-col gap-y-6'>
            <p className='text-content-primary text-lg font-medium'>Restart Wi-Fi interface ({deviceId})</p>
            <p className='text-content-secondary text-md'>Select the band to restart.</p>

            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='availableVersion'>
                Wi-Fi Band
              </AxonLabel>
              <AxonSelect value={selectedBand} onValueChange={setSelectedBand}>
                <AxonSelectTrigger>
                  <AxonSelectValue placeholder='Wi-Fi Band' />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  {wifibands.map((version) => (
                    <AxonSelectItem key={version} value={version}>
                      {version}
                    </AxonSelectItem>
                  ))}
                </AxonSelectContent>
              </AxonSelect>
            </div>
          </div>

          <AxonAlertDialogFooter className='mt-6'>
            <AxonAlertDialogCancel>Cancel</AxonAlertDialogCancel>
            <AxonAlertDialogAction disabled={!selectedBand} onClick={() => setOpenConfirmDialog(true)}>
              Restart
            </AxonAlertDialogAction>
          </AxonAlertDialogFooter>
        </AxonAlertDialogContent>
      </AxonAlertDialog>

      <AxonAlertDialog open={openConfirmDialog} onOpenChange={setOpenConfirmDialog}>
        <AxonAlertDialogContent className='min-w-[350px] gap-0'>
          <AxonAlertDialogHeader>
            <AxonAlertDialogTitle></AxonAlertDialogTitle>
            <AxonAlertDialogDescription></AxonAlertDialogDescription>
          </AxonAlertDialogHeader>
          <div className='flex flex-col gap-y-6'>
            <p className='text-content-primary text-lg font-medium'>Restart Wi-Fi interface ({deviceId})</p>
            <p className='text-content-secondary text-md'>Are you sure you want to reset this interface?</p>

            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='wifiBand'>
                Wi-Fi Band
              </AxonLabel>
              <AxonInput value={selectedBand} disabled />
            </div>
          </div>

          <AxonAlertDialogFooter className='mt-6'>
            <AxonAlertDialogCancel>Cancel</AxonAlertDialogCancel>
            <AxonAlertDialogAction disabled={!selectedBand} onClick={() => setOpenConfirmDialog(true)}>
              Confirm
            </AxonAlertDialogAction>
          </AxonAlertDialogFooter>
        </AxonAlertDialogContent>
      </AxonAlertDialog>
    </>
  );
};

export default RestartWifiInterfaceDialog;
