import { AxonAlertDialogWrapper } from 'ui/UIComponents';
type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceId: string;
};
const FactoryResetDialog = ({ deviceId, onOpenChange, open }: Props) => {
  return (
    <AxonAlertDialogWrapper
      open={open}
      onOpenChange={onOpenChange}
      title='Factory reset'
      description={`Are you sure you want to perform this action on the device “${deviceId}”?`}
      confirmText='Confirm'
      cancelText='Cancel'
      onConfirm={() => {}}
    />
  );
};

export default FactoryResetDialog;
