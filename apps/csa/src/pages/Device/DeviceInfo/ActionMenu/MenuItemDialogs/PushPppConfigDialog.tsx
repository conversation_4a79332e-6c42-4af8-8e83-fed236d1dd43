import {
  AxonAlertDialog,
  AxonAlertDialogAction,
  AxonAlertDialogCancel,
  AxonAlertDialogContent,
  AxonAlertDialogDescription,
  AxonAlertDialogFooter,
  AxonAlertDialogHeader,
  AxonAlertDialogTitle,
  AxonInput,
  AxonLabel,
  AxonSelect,
  AxonSelectContent,
  AxonSelectItem,
  AxonSelectTrigger,
  AxonSelectValue,
} from 'ui/UIComponents';

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceId: string;
};

const PushPppConfigDialog = ({ deviceId, onOpenChange, open }: Props) => {
  return (
    <AxonAlertDialog open={open} onOpenChange={onOpenChange}>
      <AxonAlertDialogContent className='min-w-[640px] gap-0'>
        <AxonAlertDialogHeader>
          <AxonAlertDialogTitle></AxonAlertDialogTitle>
          <AxonAlertDialogDescription></AxonAlertDialogDescription>
        </AxonAlertDialogHeader>
        <div className='flex flex-col gap-y-6'>
          <p className='text-content-primary text-lg font-medium'>Push PPP config ({deviceId})</p>
          <p className='text-content-secondary text-md'>Enter options for pushing the PPP config.</p>
          <div className='flex flex-row items-center gap-x-3'>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='username'>
                Username
              </AxonLabel>
              <AxonInput id='username' placeholder='Username' />
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='password'>
                Password
              </AxonLabel>
              <AxonInput type='password' id='password' placeholder='Password' />
            </div>
          </div>
          <div className='flex flex-row items-center gap-x-3'>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='protocolType'>
                Protocol type
              </AxonLabel>
              <AxonSelect defaultValue='pppoe'>
                <AxonSelectTrigger>
                  <AxonSelectValue placeholder='Protocol type' />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  <AxonSelectItem value='pppoe'>PPPoE (default)</AxonSelectItem>
                </AxonSelectContent>
              </AxonSelect>
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='serviceName'>
                Service Name (Optional)
              </AxonLabel>
              <AxonInput type='serviceName' id='serviceName' placeholder='Service Name' />
            </div>
          </div>
          <div className='flex flex-row items-center gap-x-3'>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='mtu'>
                MTU size (optional)
              </AxonLabel>
              <AxonInput id='mtu' placeholder='MTU' value={1492} />
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='authenticationMethod'>
                Authentication method
              </AxonLabel>
              <AxonSelect defaultValue='pap'>
                <AxonSelectTrigger>
                  <AxonSelectValue placeholder='Authentication method' />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  <AxonSelectItem value='pap'>PAP (auto)</AxonSelectItem>
                </AxonSelectContent>
              </AxonSelect>
            </div>
          </div>
          <div className='flex flex-row items-center gap-x-3'>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='connectionMode'>
                Connection Mode
              </AxonLabel>
              <AxonSelect defaultValue='alwaysOn'>
                <AxonSelectTrigger>
                  <AxonSelectValue placeholder='Connection Mode' />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  <AxonSelectItem value='alwaysOn'>Always On</AxonSelectItem>
                </AxonSelectContent>
              </AxonSelect>
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='ipMode'>
                IP Mode
              </AxonLabel>
              <AxonSelect defaultValue='Dynamic'>
                <AxonSelectTrigger>
                  <AxonSelectValue placeholder='IP Mode' />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  <AxonSelectItem value='Dynamic'>Dynamic (Default)</AxonSelectItem>
                </AxonSelectContent>
              </AxonSelect>
            </div>
          </div>
        </div>
        <AxonAlertDialogFooter className='mt-10'>
          <AxonAlertDialogCancel>Cancel</AxonAlertDialogCancel>
          <AxonAlertDialogAction>Confirm</AxonAlertDialogAction>
        </AxonAlertDialogFooter>
      </AxonAlertDialogContent>
    </AxonAlertDialog>
  );
};

export default PushPppConfigDialog;
