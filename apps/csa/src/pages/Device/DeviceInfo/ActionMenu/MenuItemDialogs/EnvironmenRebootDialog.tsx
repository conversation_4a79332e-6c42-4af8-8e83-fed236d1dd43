import { AxonAlertDialogWrapper } from 'ui/UIComponents';
type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceId: string;
};
const EnvironmenRebootDialog = ({ deviceId, onOpenChange, open }: Props) => {
  return (
    <AxonAlertDialogWrapper
      open={open}
      onOpenChange={onOpenChange}
      title='Environment reboot'
      description={`Are you sure you want to perform this action on the device “${deviceId}”?`}
      confirmText='Confirm'
      cancelText='Cancel'
      onConfirm={() => {}}
    />
  );
};

export default EnvironmenRebootDialog;
