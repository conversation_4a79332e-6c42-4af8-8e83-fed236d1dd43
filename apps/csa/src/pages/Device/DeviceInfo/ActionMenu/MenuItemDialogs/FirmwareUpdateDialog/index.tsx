import { useState } from 'react';
import {
  AxonAlertDialog,
  AxonAlertDialogAction,
  AxonAlertDialogCancel,
  AxonAlertDialogContent,
  AxonAlertDialogDescription,
  AxonAlertDialogFooter,
  AxonAlertDialogHeader,
  AxonAlertDialogTitle,
  AxonInput,
  AxonLabel,
  AxonSelect,
  AxonSelectContent,
  AxonSelectItem,
  AxonSelectTrigger,
  AxonSelectValue,
} from 'ui/UIComponents';

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceId: string;
};

const FirmwareUpdateDialog = ({ deviceId, onOpenChange, open }: Props) => {
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

  const [selectedVersion, setSelectedVersion] = useState('');
  const currentVersion = '1.0.7.213241';
  const availableVersions = ['1.0.11.213892', '1.0.10.213892', '1.0.9.213892', '1.0.8.213892', '1.0.7.213892'];

  return (
    <>
      <AxonAlertDialog open={open} onOpenChange={onOpenChange} key={deviceId}>
        <AxonAlertDialogContent className='min-w-[350px] gap-0'>
          <AxonAlertDialogHeader>
            <AxonAlertDialogTitle></AxonAlertDialogTitle>
            <AxonAlertDialogDescription></AxonAlertDialogDescription>
          </AxonAlertDialogHeader>
          <div className='flex flex-col gap-y-6'>
            <p className='text-content-primary text-lg font-medium'>Update firmware ({deviceId})</p>
            <p className='text-content-secondary text-md'>Select the new firmware to install.</p>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='currentVersion'>
                Current Version
              </AxonLabel>
              <AxonInput id='currentVersion' placeholder='Current Version' value={currentVersion} disabled />
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='availableVersion'>
                Available Version
              </AxonLabel>
              <AxonSelect value={selectedVersion} onValueChange={setSelectedVersion}>
                <AxonSelectTrigger>
                  <AxonSelectValue placeholder='Available Version' />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  {availableVersions.map((version) => (
                    <AxonSelectItem key={version} value={version}>
                      {version}
                    </AxonSelectItem>
                  ))}
                </AxonSelectContent>
              </AxonSelect>
            </div>
          </div>

          <AxonAlertDialogFooter className='mt-6'>
            <AxonAlertDialogCancel>Cancel</AxonAlertDialogCancel>
            <AxonAlertDialogAction disabled={!selectedVersion} onClick={() => setOpenConfirmDialog(true)}>
              Update
            </AxonAlertDialogAction>
          </AxonAlertDialogFooter>
        </AxonAlertDialogContent>
      </AxonAlertDialog>

      <AxonAlertDialog open={openConfirmDialog} onOpenChange={setOpenConfirmDialog}>
        <AxonAlertDialogContent className='min-w-[350px] gap-0'>
          <AxonAlertDialogHeader>
            <AxonAlertDialogTitle></AxonAlertDialogTitle>
            <AxonAlertDialogDescription></AxonAlertDialogDescription>
          </AxonAlertDialogHeader>
          <div className='flex flex-col gap-y-6'>
            <p className='text-content-primary text-lg font-medium'>Update firmware ({deviceId})</p>
            <p className='text-content-secondary text-md'>Are you sure you would like to update the firmware?</p>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='currentVersion'>
                Current Version
              </AxonLabel>
              <AxonInput id='currentVersion' placeholder='Current Version' value={currentVersion} disabled />
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='availableVersion'>
                Available Version
              </AxonLabel>
              <AxonInput id='availableVersion' placeholder='Available Version' value={selectedVersion} disabled />
            </div>
          </div>

          <AxonAlertDialogFooter className='mt-6'>
            <AxonAlertDialogCancel>Cancel</AxonAlertDialogCancel>
            <AxonAlertDialogAction>Confirm</AxonAlertDialogAction>
          </AxonAlertDialogFooter>
        </AxonAlertDialogContent>
      </AxonAlertDialog>
    </>
  );
};

export default FirmwareUpdateDialog;
