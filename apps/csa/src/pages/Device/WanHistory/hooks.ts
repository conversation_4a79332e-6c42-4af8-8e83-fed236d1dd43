import { useGetWanHistory } from 'services/CPEService';
import { getUnixTime } from 'services/Utils';

export const useWanHistoryChart = ({
  customerId,
  deviceId,
  startDate,
  endDate,
  type,
}: {
  customerId: string;
  deviceId: string;
  startDate: string | null;
  endDate: string | null;
  type: string;
}) => {
  const { data, isLoading, isError } = useGetWanHistory(
    {
      customerId,
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
      type,
    },
    { enabled: Boolean(customerId) && Boolean(deviceId) && Boolean(startDate) && Boolean(type) },
  );

  const chartData = data?.data || [];
  const lineProps =
    data?.data && Array.isArray(data.data) && data.data.length > 0
      ? Object.keys(data.data[0])
          .filter((key) => key !== 'date')
          .map((item, index) => {
            if (item === 'internetUsage') {
              return { dataKey: item, color: `rgb(var(--chart-${(index % 30) + 1}))`, name: 'Internet Usage' };
            }
            return { dataKey: item, color: `rgb(var(--chart-${(index % 30) + 1}))` };
          })
      : [];

  return { chartData, lineProps, isLoading, isError };
};
