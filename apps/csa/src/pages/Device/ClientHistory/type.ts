export interface CpeClient {
  name: string;
  mac: string;
  type: string;
  throughputBandAvailable: Record<string, boolean>;
  latencyBandAvailable: Record<string, boolean>;
  rssiBandAvailable: Record<string, boolean>;
  snrBandAvailable: Record<string, boolean>;
  txPhyRateBandAvailable: Record<string, boolean>;
  rxPhyRateBandAvailable: Record<string, boolean>;
  trafficUpBandAvailable: Record<string, boolean>;
  trafficDownBandAvailable: Record<string, boolean>;
  qoeBandAvailable: boolean;
}

export interface CpeBand {
  bandId: string;
  band: string;
  label: string;
  networkType: string;
}

export interface CpeClientStation {
  rssi: number | null;
  band: string;
  snr: number | null;
  throughput: number | null;
  latency: number | null;
}

export interface CpeClientHistoryDatapoint {
  date: number;
  stations: Record<string, Record<string, CpeClientStation>>;
}

export interface ClientHistoryResponse {
  datapoints: CpeClientHistoryDatapoint[];
  clients: Record<string, CpeClient>;
  bands: CpeBand[];
}

export type ClientHistoryParams = {
  customerId: string;
  deviceId: string;
  startDate: number;
  endDate?: number;
};
