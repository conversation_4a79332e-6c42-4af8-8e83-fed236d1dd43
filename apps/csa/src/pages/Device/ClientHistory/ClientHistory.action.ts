import useTabStore from '@/stores/tab.store';
import { useGetCpeClientHistory, useGetCpeInfo } from 'services/CPEService';
import { useShallow } from 'zustand/react/shallow';
import { getDateFromUnixTime, getUnixTime } from 'services/Utils';
import get from 'lodash/get';
import { useState, useMemo, useEffect } from 'react';
import { CpeBand, CpeClient } from './type';
import isEmpty from 'lodash/isEmpty';

export enum EMetric {
  WIFI_PHY_RATE = 'phyRate',
  WIFI_THROUGHPUT = 'throughput',
  WIFI_TRAFFIC_DOWN = 'trafficDown',
  WIFI_TRAFFIC_UP = 'trafficUp',
  LATENCY = 'latency',
  SNR = 'snr',
  RSSI = 'rssi',
  QOE = 'qoe',
}

const ETHERNET_METRICS = [EMetric.WIFI_TRAFFIC_DOWN, EMetric.WIFI_TRAFFIC_UP, EMetric.LATENCY];

const lineChartConfig = {
  [EMetric.WIFI_THROUGHPUT]: {
    yAxisProps: [
      {
        label: {
          value: 'Mbps',
          dx: -20,
          angle: -90,
        },
      },
    ],
  },
  [EMetric.LATENCY]: {
    yAxisProps: [
      {
        label: {
          value: 'ms',
          dx: -20,
          angle: -90,
        },
      },
    ],
  },
  [EMetric.RSSI]: {
    yAxisProps: [
      {
        label: {
          value: 'dBm',
          dx: -20,
          angle: -90,
        },
      },
    ],
  },
  [EMetric.SNR]: {
    yAxisProps: [
      {
        label: {
          value: 'dB',
          dx: -20,
          angle: -90,
        },
      },
    ],
  },
  [EMetric.WIFI_TRAFFIC_DOWN]: {
    yAxisProps: [
      {
        label: {
          value: 'MB',
          dx: -20,
          angle: -90,
        },
      },
    ],
  },
  [EMetric.WIFI_TRAFFIC_UP]: {
    yAxisProps: [
      {
        label: {
          value: 'MB',
          dx: -20,
          angle: -90,
        },
      },
    ],
  },
  [EMetric.WIFI_PHY_RATE]: {
    yAxisProps: [
      {
        label: {
          value: 'Mbps',
          dx: -20,
          angle: -90,
        },
      },
    ],
  },
  [EMetric.QOE]: {
    yAxisProps: [
      {
        label: {
          value: 'QoE',
          dx: -50,
          angle: -90,
        },
      },
    ],
  },
};

function generateConfig(metric: EMetric, listOfKeys: { key: string; name: string }[]) {
  return {
    linesProps: listOfKeys.map(({ key, name }, i) => {
      return {
        dataKey: key,
        color: `rgb(var(--chart-${(i % 30) + 1}))`, // max 30 variables colors predefined in global.css
        name: name,
        connectNulls: metric === EMetric.QOE,
      };
    }),
    yAxisProps: lineChartConfig[metric].yAxisProps,
  };
}

export const useClientHistoryAction = () => {
  const { activeTab } = useTabStore(
    useShallow((state) => ({
      activeTab: state.activeTab,
    })),
  );
  const lineId = activeTab?.deviceId || activeTab?.customerId;
  const startDate = get(activeTab, 'timeRangeSelected.startDate');
  const endDate = get(activeTab, 'timeRangeSelected.endDate');

  const { data: cpeInfo } = useGetCpeInfo(lineId ?? '', { enabled: !!lineId, staleTime: Infinity });
  const customerId = get(cpeInfo, 'data.customerId') ?? ''; // id of account
  const deviceId = get(cpeInfo, 'data.id') ?? ''; // id of cpe

  const [filter, setFilter] = useState<{
    client: CpeClient[];
    metric: EMetric;
    band: CpeBand | null;
  }>({
    client: [],
    metric: EMetric.WIFI_THROUGHPUT,
    band: null,
  });

  const { data, ...rest } = useGetCpeClientHistory(
    {
      customerId,
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: !!customerId && !!deviceId && !!startDate,
    },
  );

  const chartData = useMemo(() => {
    const { client: filteredClient, metric, band: bandFiltered } = filter;
    const points = get(data, 'data.datapoints') ?? [];
    const formatData: Record<string, unknown>[] = [];
    const listOfKeys = {};
    if (points && filteredClient.length > 0) {
      points.forEach((point) => {
        const { date, stations } = point;
        const listDataForClients = {};
        filteredClient.forEach((client) => {
          const bandId = bandFiltered?.bandId || '';
          if (metric === EMetric.WIFI_THROUGHPUT && !client.throughputBandAvailable[bandId]) {
            return;
          }
          if (metric === EMetric.LATENCY && !client.latencyBandAvailable[bandId]) {
            return;
          }
          if (metric === EMetric.RSSI && !client.rssiBandAvailable[bandId]) {
            return;
          }
          if (metric === EMetric.SNR && !client.snrBandAvailable[bandId]) {
            return;
          }
          if (metric === EMetric.WIFI_TRAFFIC_DOWN && !client.trafficDownBandAvailable[bandId]) {
            return;
          }
          if (metric === EMetric.WIFI_TRAFFIC_UP && !client.trafficUpBandAvailable[bandId]) {
            return;
          }
          if (metric === EMetric.QOE && !client.qoeBandAvailable) {
            return;
          }
          if (
            metric === EMetric.WIFI_PHY_RATE &&
            !client.txPhyRateBandAvailable[bandId] &&
            !client.rxPhyRateBandAvailable[bandId]
          ) {
            return;
          }

          let key = `${client.mac}.${bandId}.${metric}`;
          if (metric === EMetric.QOE) {
            key = `${client.mac}.qoe`;
          }
          const metricPointValue = get(stations, key) as unknown as number;
          listDataForClients[key] = metricPointValue;
          if (!listOfKeys[key]) {
            listOfKeys[key] = {
              key,
              name: client.name ? `${client.name} (${client.mac})` : client.mac,
            };
          }
        });

        if (!isEmpty(listDataForClients)) {
          if (metric === EMetric.QOE) {
            // only push on start of the day to remove hour from now
            const day = getDateFromUnixTime(date)?.utc();
            if (day?.startOf('day').valueOf() === date) {
              formatData.push({
                date,
                ...listDataForClients,
              });
            }
          } else {
            formatData.push({
              date,
              ...listDataForClients,
            });
          }
        }
      });
    }
    return {
      config: generateConfig(metric, Object.values(listOfKeys)),
      data: formatData,
    };
  }, [filter, data]);
  const clientList = useMemo<CpeClient[]>(() => {
    const clientData = get(data, 'data.clients') ?? {};
    return Object.values(clientData);
  }, [data]);

  useEffect(() => {
    const bands = get(data, 'data.bands') ?? [];
    const clientList = get(data, 'data.clients') ?? [];
    if (clientList) {
      setFilter((prev) => ({
        ...prev,
        client: clientList,
      }));
    }
    if (bands) {
      setFilter((prev) => ({
        ...prev,
        band: bands[0],
      }));
    }
  }, [data, setFilter]);
  const selectedMetric = filter.metric;

  const bands = useMemo(() => {
    const listNetworkbands = get(data, 'data.bands') ?? [];

    // if metric is not belong to ethernet, return only wi-fi bands (only wifi has networkType)
    if (!ETHERNET_METRICS.includes(selectedMetric)) {
      return listNetworkbands.filter((band) => band.networkType);
    }
    return listNetworkbands;
  }, [data, selectedMetric]);

  return {
    ...rest,
    data,
    filter,
    setFilter,
    chartData,
    clientList,
    bands,
  };
};
