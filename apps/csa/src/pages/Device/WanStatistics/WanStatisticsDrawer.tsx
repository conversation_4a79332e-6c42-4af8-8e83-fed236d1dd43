import useTabStore, { EUnit } from '@/stores/tab.store';
import { cn } from '@/utils';
import { ColumnDef, getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { Search } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetWanStatisticsDrawer } from 'services/CPEService';
import { getUnixTime } from 'services/Utils';
import { AxonDateRangePicker, AxonInput, AxonTableData } from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';

export interface IWanStatistics {
  wanId: string;
  wanType: string;
  wanDown: string | number | null;
  wanDownIssue: string | null;
  wanUp: string | number | null;
  wanUpIssue: string | null;
  wanDescription: string | null;
  wanUnit: string;
}

type IWanDetail = {
  wanId: string;
  wanType: string;
  wanMinValue: string | number | null;
  wanMaxValue: string | number | null;
  wanAverage: string | number | null;
  wanValue: string | number | null;
  noOfTest: number | null;
  wanUnit: string;
};

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

const disabledDate = {
  after: new Date(),
};

const WanStatisticsDrawer = () => {
  const { t } = useTranslation();
  const activeTab = useTabStore((state) => state.activeTab);
  const { setTimeRange } = useTabStore(
    useShallow((state) => ({
      setTimeRange: state.setTimeRange,
    })),
  );

  const [searchKey, setSearchKey] = useState('');

  const { data: queryWanStatisticDrawerData } = useGetWanStatisticsDrawer({
    customerId: activeTab?.customerId || '',
    deviceId: activeTab?.deviceId || '',
    startDate: getUnixTime(activeTab?.timeRangeSelected?.startDate) || 0,
    endDate: getUnixTime(activeTab?.timeRangeSelected?.endDate) || undefined,
  });
  const wanStatisticDrawerData = queryWanStatisticDrawerData?.data;
  const { results: detail } = wanStatisticDrawerData ?? {};

  const handleSetCustomTimeRange = (value?: DateRange) => {
    setTimeRange({
      unit: EUnit.CUSTOM,
      startDate: value?.from || null,
      endDate: value?.to || null,
    });
  };

  const columns = useMemo<ColumnDef<IWanDetail, any>[]>(() => {
    return [
      {
        header: () => <span className='pl-5'>Type</span>,
        accessorKey: 'type',
        cell: ({ row }) => {
          const { wanType } = row.original;
          return <span className='text-md text-content-primary pl-5 font-medium'>{wanType}</span>;
        },
      },
      {
        header: 'Min-Max',
        accessorKey: `minMax`,
        cell: ({ row }) => {
          const { wanMinValue, wanMaxValue, wanUnit } = row.original;
          return (
            <span className='text-content-primary text-md font-normal'>
              {wanUnit
                ? `${wanMinValue !== null ? wanMinValue : ''} - ${wanMaxValue !== null ? wanMaxValue : ''} ${wanUnit}`
                : ''}
            </span>
          );
        },
      },
      {
        header: 'Average',
        accessorKey: 'averageValue',
        cell: ({ row }) => {
          const { wanAverage, wanUnit } = row.original;
          return (
            <span className='text-md text-content-primary font-normal'>
              {wanAverage !== null ? wanAverage : '-'} {wanUnit}
            </span>
          );
        },
      },
      {
        header: 'Latest result',
        accessorKey: 'value',
        cell: ({ row }) => {
          const { wanValue, wanUnit, wanId } = row.original;
          const isWarning = wanId.includes('speed') || wanId.includes('latency');

          return (
            <div className='flex flex-col items-start'>
              <span className='text-content-primary'>
                {wanValue !== null ? wanValue : '-'} {wanUnit}
              </span>
              {isWarning && (
                <span className='flex items-center gap-1'>
                  {/* <BoldArrowUpSquare color='rgb(var(--content-meta-green))' />{' '}
                  <span className='text-content-meta-green text-xs font-medium'>xx%</span>{' '}
                  <span className='font-book text-xs opacity-60'>vs. SLA (xxMbps)</span> */}
                </span>
              )}
            </div>
          );
        },
      },
      {
        header: () => <span className='pr-5'>No. of tests</span>,
        accessorKey: 'noOfTest',
        cell: ({ row }) => {
          const { noOfTest } = row.original;
          return (
            <span className='text-md text-content-primary pr-5 font-normal'>{noOfTest !== null ? noOfTest : ''}</span>
          );
        },
      },
    ];
  }, []);

  const rows = useMemo<IWanDetail[]>(() => {
    if (!searchKey || !detail) return detail || [];
    return detail.filter((item) => {
      if (item.wanId.toLowerCase().includes(searchKey.toLowerCase())) return true;
      if (item.wanType.toLowerCase().includes(searchKey.toLowerCase())) return true;
      if ((item.wanUnit ?? '').toLowerCase().includes(searchKey.toLowerCase())) return true;

      return false;
    });
  }, [detail, searchKey]);

  const table = useReactTable({
    columns,
    data: rows,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return (
    <div className='flex h-screen flex-col'>
      <div className={cn('h-16 border-b px-6 py-5 text-xl font-medium')}>{t('wanStatistics')}</div>
      <div className={cn('border-b-border-flat flex items-center justify-between border-b p-4')}>
        <div className='relative w-96'>
          <Search size={16} className='text-muted-foreground absolute left-3 top-3' />
          <AxonInput
            placeholder='Search by type'
            className='pl-10'
            value={searchKey}
            onChange={(e) => setSearchKey(e.target.value)}
          />
        </div>
        <div>
          <AxonDateRangePicker
            selected={{
              from: activeTab?.timeRangeSelected?.startDate ?? undefined,
              to: activeTab?.timeRangeSelected?.endDate ?? undefined,
            }}
            disabled={disabledDate}
            onSelect={handleSetCustomTimeRange}
            showIcon={false}
          />
        </div>
      </div>

      <div className={cn('bg-surface-section scrollbar-lg h-full overflow-auto')}>
        <AxonTableData
          table={table}
          showFooter={false}
          headerRowClassName='bg-surface-section'
          bodyRowClassName='bg-surface-section'
        />
      </div>
    </div>
  );
};

export default WanStatisticsDrawer;
