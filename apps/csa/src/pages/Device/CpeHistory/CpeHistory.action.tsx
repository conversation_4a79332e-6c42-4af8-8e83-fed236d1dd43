import { useGetCpeHistory, useGetCpeInfo } from 'services/CPEService';
import { getUnixTime } from 'services/Utils';
import useTabStore from '@/stores/tab.store';
import { useMemo, useState } from 'react';
import get from 'lodash/get';
import { useShallow } from 'zustand/react/shallow';
import { TCpeHistoryDataPoint } from './type';
import isEmpty from 'lodash/isEmpty';

export enum ChartMetric {
  CPU_MEMORY_USAGE = 'cpuMemoryUsage',
  CHIPSET_TEMPERATURE = 'chipsetTemperature',
  POWER_CYCLE = 'powerCycle',
  QOE = 'qoe',
  RSSI = 'rssi',
  DISCONNECTION_EVENT = 'disconnEvents',
}

const lineChartConfig = {
  [ChartMetric.CPU_MEMORY_USAGE]: {
    yAxisProps: [
      {
        yAxisId: 'cpuUsageLeft',
        label: {
          value: 'CPU Usage (%)',
          dx: -20,
          angle: -90,
        },
        domain: [0, 100],
      },
      {
        yAxisId: 'cpuLoadLeft',
        label: {
          value: 'CPU Load',
          dx: -20,
          angle: -90,
        },
        domain: [0, 2],
      },
      {
        yAxisId: 'freeMemoryRight',
        label: {
          value: 'Free Memory (MB)',
          dx: 20,
          angle: 90,
        },
        orientation: 'right' as const,
      },
    ],
    linesProps: [
      {
        dataKey: 'cpuUsage',
        color: 'rgb(var(--chart-1))',
        yAxisId: 'cpuUsageLeft',
        name: 'CPU Usage',
      },
      {
        dataKey: 'cpuLoad',
        color: 'rgb(var(--chart-2))',
        yAxisId: 'cpuLoadLeft',
        name: 'CPU Load',
      },
      {
        dataKey: 'freeMemory',
        color: 'rgb(var(--chart-3))',
        yAxisId: 'freeMemoryRight',
        name: 'Free Memory',
      },
    ],
  },
  [ChartMetric.CHIPSET_TEMPERATURE]: {
    yAxisProps: [
      {
        yAxisId: 'chipsetTemperature',
        label: {
          value: 'Chipset Temperature (°C)',
          dx: -20,
          angle: -90,
        },
      },
    ],
    linesProps: [
      {
        dataKey: 'chipsetTemperature',
        color: 'rgb(var(--chart-1))',
        yAxisId: 'chipsetTemperature',
        name: 'Chipset Temperature',
      },
    ],
  },
  [ChartMetric.POWER_CYCLE]: {
    yAxisProps: [
      {
        yAxisId: 'powerCycle',
        allowDecimals: false,
        label: {
          value: 'Power Cycle',
          dx: -30,
          angle: -90,
        },
      },
    ],
    linesProps: [
      {
        dataKey: 'powerCycle',
        color: 'rgb(var(--chart-1))',
        yAxisId: 'powerCycle',
        name: 'Power Cycle',
        connectNulls: true,
      },
    ],
  },
  [ChartMetric.QOE]: {
    yAxisProps: [
      {
        yAxisId: 'qoe',
        label: {
          value: 'Broadband QoE',
          dx: -50,
          angle: -90,
        },
      },
    ],
    linesProps: [
      {
        dataKey: 'qoe',
        color: 'rgb(var(--chart-1))',
        yAxisId: 'qoe',
        name: 'QoE',
        connectNulls: true,
      },
    ],
  },
  [ChartMetric.RSSI]: {
    yAxisProps: [
      {
        label: {
          value: 'RSSI',
          dx: -30,
          angle: -90,
        },
      },
    ],
    linesProps: [] as any,
  },
  [ChartMetric.DISCONNECTION_EVENT]: {
    yAxisProps: [
      {
        allowDecimals: false,
        label: {
          value: 'Disconnection Event',
          dx: -30,
          angle: -90,
        },
      },
    ],
    linesProps: [] as any,
  },
};

const METRIC_FOR_EXTENDER = [ChartMetric.RSSI, ChartMetric.DISCONNECTION_EVENT];

function generateConfig(metric: ChartMetric, listLines: Record<string, { key: string }>) {
  const config = lineChartConfig[metric];
  if (METRIC_FOR_EXTENDER.includes(metric) && !isEmpty(listLines)) {
    config.linesProps = Object.values(listLines).map((line, index) => {
      return {
        ...line,
        dataKey: line.key,
        color: `rgb(var(--chart-${(index % 30) + 1}))`,
      };
    });
  }
  return config;
}

export const useCpeHistoryAction = () => {
  const { activeTab } = useTabStore(
    useShallow((state) => ({
      activeTab: state.activeTab,
    })),
  );
  const lineId = activeTab?.deviceId || activeTab?.customerId;
  const startDate = get(activeTab, 'timeRangeSelected.startDate');
  const endDate = get(activeTab, 'timeRangeSelected.endDate');

  const { data: cpeInfo, isLoading: isCpeInfoLoading } = useGetCpeInfo(lineId ?? '', {
    enabled: !!lineId,
    staleTime: Infinity,
  });
  const customerId = get(cpeInfo, 'data.customerId') ?? ''; // id of account
  const deviceId = get(cpeInfo, 'data.id') ?? ''; // id of cpe
  const isExtender = customerId !== deviceId;
  const [selectedMetricType, setSelectedMetricType] = useState<ChartMetric>(ChartMetric.QOE);

  const {
    data,
    isLoading: isCpeHistoryLoading,
    isError,
  } = useGetCpeHistory(
    {
      customerId,
      deviceId: deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: !!customerId && !!deviceId && !!startDate,
    },
  );

  const chartData = useMemo(() => {
    const results = get(data, `data.datapoints`) ?? [];
    const listLines = {};
    let datapoints = results;

    if (METRIC_FOR_EXTENDER.includes(selectedMetricType)) {
      datapoints = [];
      (results as TCpeHistoryDataPoint[]).forEach((point) => {
        const { date, ...rest } = point;
        const metricValue = get(rest, `${selectedMetricType}`) || {};
        const data = {
          date,
        };
        Object.entries(metricValue).map(([bandId, datapointValue]) => {
          listLines[bandId] = {
            key: bandId,
          };
          data[bandId] = datapointValue;
        });
        datapoints.push(data);
      });
    }

    if (selectedMetricType === ChartMetric.QOE) {
      datapoints = (results as TCpeHistoryDataPoint[]).filter((point) => point.qoe !== null);
    } else if (selectedMetricType === ChartMetric.POWER_CYCLE) {
      datapoints = (results as TCpeHistoryDataPoint[]).filter((point) => point.powerCycle !== null);
    }
    return {
      config: generateConfig(selectedMetricType, listLines),
      data: datapoints,
    };
  }, [selectedMetricType, data]);

  return {
    selectedMetricType,
    setSelectedMetricType,
    chartData,
    isLoading: isCpeInfoLoading || isCpeHistoryLoading,
    isError,
    isExtender,
  };
};
