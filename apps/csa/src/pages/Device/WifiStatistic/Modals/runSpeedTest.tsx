import { AxonButton, AxonCheckbox, AxonDialog, AxonDialogContent, AxonLabel } from 'ui/UIComponents';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';

type MoreDetailModalProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmitData: (data: string[]) => void;
};
const RunSpeedTestModal = ({ onOpenChange, open, onSubmitData }: MoreDetailModalProps) => {
  const { t } = useTranslation();
  const [boxRefresh, setBoxRefresh] = useState<string[]>([
    'Speed (Dn/Up)',
    'Latency (Dn/Up)',
    'Jitter (Dn/Up)',
    'Ch. interference/congestion',
  ]);

  const handleChecked = (status) => {
    setBoxRefresh(boxRefresh.includes(status) ? boxRefresh.filter((d) => d !== status) : [...boxRefresh, status]);
  };

  return (
    <AxonDialog open={open} onOpenChange={onOpenChange}>
      <AxonDialogContent className='bg-surface-popover max-w-xs'>
        <div className='flex flex-col gap-y-3'>
          <p className='text-content-primary text-lg font-semibold'>Wi-Fi Speed test</p>
          <p className='text-content-primary text-md'>Select the parameters you want to test.</p>
        </div>
        <div className='flex flex-col gap-y-2'>
          <div className='gap-xs px-xs flex items-center'>
            <AxonCheckbox
              id='speed'
              onCheckedChange={() => handleChecked('Speed (Dn/Up)')}
              checked={boxRefresh.includes('Speed (Dn/Up)')}
            />
            <AxonLabel className='text-content-secondary' htmlFor='speed'>
              Speed
            </AxonLabel>
          </div>
          <div className='gap-xs px-xs flex items-center'>
            <AxonCheckbox
              id='latency'
              onCheckedChange={() => handleChecked('Latency (Dn/Up)')}
              checked={boxRefresh.includes('Latency (Dn/Up)')}
            />
            <AxonLabel className='text-content-secondary' htmlFor='latency'>
              Latency
            </AxonLabel>
          </div>
          <div className='gap-xs px-xs flex items-center'>
            <AxonCheckbox
              id='jitter'
              onCheckedChange={() => handleChecked('Jitter (Dn/Up)')}
              checked={boxRefresh.includes('Jitter (Dn/Up)')}
            />
            <AxonLabel className='text-content-secondary' htmlFor='jitter'>
              Jitter
            </AxonLabel>
          </div>
          <div className='gap-xs px-xs flex items-center'>
            <AxonCheckbox
              id='congestion'
              onCheckedChange={() => handleChecked('Ch. interference/congestion')}
              checked={boxRefresh.includes('Ch. interference/congestion')}
            />
            <AxonLabel className='text-content-secondary' htmlFor='congestion'>
              Congestion
            </AxonLabel>
          </div>
        </div>
        <div className='mt-4 grid grid-cols-2 gap-4'>
          <AxonButton variant='outline' onClick={() => onOpenChange(false)}>
            {t('cancel')}
          </AxonButton>
          <AxonButton variant='accent' type='submit' onClick={() => onSubmitData(boxRefresh)}>
            {t('Start')}
          </AxonButton>
        </div>
      </AxonDialogContent>
    </AxonDialog>
  );
};

export default RunSpeedTestModal;
