import { AxonSkeletonLoader } from 'ui/UIComponents';

const Skeleton = () => {
  return (
    <div className='flex flex-col'>
      <div className='flex items-center divide-x-2'>
        <div className='flex flex-1 flex-col gap-y-2 p-5 pl-0'>
          <AxonSkeletonLoader className='h-4 w-16' />
          <AxonSkeletonLoader className='h-4 w-32' />
        </div>
        <div className='flex flex-1 flex-col gap-y-2 p-5'>
          <AxonSkeletonLoader className='h-4 w-16' />
          <AxonSkeletonLoader className='h-4 w-32' />
        </div>
        <div className='flex flex-1 flex-col gap-y-2 p-5'>
          <AxonSkeletonLoader className='h-4 w-16' />
          <AxonSkeletonLoader className='h-4 w-32' />
        </div>
        <div className='flex flex-1 flex-col gap-y-2 p-5'>
          <AxonSkeletonLoader className='h-4 w-16' />
          <AxonSkeletonLoader className='h-4 w-32' />
        </div>
      </div>
      <div className='border-gradient-border flex items-center divide-x-2 border-t'>
        <div className='flex flex-1 flex-col gap-y-2 p-5 pl-0'>
          <AxonSkeletonLoader className='h-4 w-16' />
          <AxonSkeletonLoader className='h-4 w-32' />
          <AxonSkeletonLoader className='h-4 w-32' />
        </div>
        <div className='flex flex-1 flex-col gap-y-2 p-5'>
          <AxonSkeletonLoader className='h-4 w-16' />
          <AxonSkeletonLoader className='h-4 w-32' />
          <AxonSkeletonLoader className='h-4 w-32' />
        </div>
        <div className='flex flex-1 flex-col gap-y-2 p-5'>
          <AxonSkeletonLoader className='h-4 w-16' />
          <AxonSkeletonLoader className='h-4 w-32' />
          <AxonSkeletonLoader className='h-4 w-32' />
        </div>
        <div className='flex flex-1 flex-col gap-y-2 p-5'>
          <AxonSkeletonLoader className='h-4 w-16' />
          <AxonSkeletonLoader className='h-4 w-32' />
          <AxonSkeletonLoader className='h-4 w-32' />
        </div>
      </div>
    </div>
  );
};

export default Skeleton;
