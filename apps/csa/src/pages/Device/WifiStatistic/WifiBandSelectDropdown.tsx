import {
  AxonSelect,
  AxonSelectContent,
  AxonSelectGroup,
  AxonSelectItem,
  AxonSelectLabel,
  AxonSelectTrigger,
  AxonSelectValue,
} from 'ui/UIComponents';
import { useEffect } from 'react';
interface Props {
  wifiBands: Array<{ id: string; title: string }>;
  selectedBandId: string;
  setSelectedBandId: (id: string) => void;
}

export const WifiBandSelectDropdown = ({ wifiBands, selectedBandId, setSelectedBandId }: Props) => {
  useEffect(() => {
    const isExist = wifiBands.find(({ id }) => id === selectedBandId);
    if (!selectedBandId || !isExist) {
      setSelectedBandId(wifiBands[0].id);
    }
  }, [selectedBandId, setSelectedBandId, wifiBands]);

  return (
    <AxonSelect value={selectedBandId} onValueChange={setSelectedBandId}>
      <AxonSelectTrigger className='w-fit gap-x-2 border-none bg-transparent'>
        <AxonSelectValue placeholder='Select Band' />
      </AxonSelectTrigger>
      <AxonSelectContent>
        <AxonSelectGroup>
          <AxonSelectLabel>Select Band</AxonSelectLabel>
          {wifiBands.map((b) => {
            return (
              <AxonSelectItem key={`${b.title}-${b.id}`} value={b.id}>
                {b.title}
              </AxonSelectItem>
            );
          })}
        </AxonSelectGroup>
      </AxonSelectContent>
    </AxonSelect>
  );
};
