export enum EDeviceTypes {
  WIFI_ROUTER = 'wifi-router',
  ROUTER = 'router',
  WIFI_ACCESS_POINT = 'wifi-access-point',
  SWITCH = 'switch',
  UNKNOWN = 'unknown',
}

export enum ENodeType {
  CAMERA_CAMCODERS_BASE_STATION = 'Camera, Camcoders & Base Station',
  CAR = 'Car',
  DESKTOP = 'Desktop',
  DISHWASHER = 'Dishwasher',
  DOORBELL_DOORLOCK = 'Doorbell / Doorlock',
  GAMING_SYSTEM = 'Gaming System',
  GENERIC_IOT = 'Generic IoT',
  HOME_SECURITY = 'Home Security',
  LAPTOP = 'Laptop',
  MEDIA_PLAYER = 'Media Player',
  OVEN = 'Oven',
  PRINTER = 'Printer',
  REFRIGERATOR = 'Refrigerator',
  SMART_PHONE = 'Smart Phone',
  TV = 'TV',
  TABLET = 'Tablet',
  THERMOSTAT = 'Thermostat',
  VACUUM_CLEANER = 'Vacuum Cleaner',
  WASHER_DRYER = 'Washer / Dryer',
  WATCH = 'Watch',
  WIFI_EXTENDER_ROUTER_CONNECTIVITY_ELEMENT = 'WiFi Extender/Router/Connectivity Element',
  WIFI_SPEAKER = 'WiFi Speaker',
  UNKNOWN = 'Unknown',
}

export enum EHealthCheckStatus {
  STABLE = 'stable',
  UNSTABLE = 'unstable',
  VERY_UNSTABLE = 'veryUnstable',
  UNKNOWN = 'unknown',
}
