import useTabStore, { ETimeRange, EUnit } from '@/stores/tab.store';
import get from 'lodash/get';
import { getDateFromToday, getDayFromTodayByDays, getDayjsFromDate } from 'services/Utils';
import { Clock } from 'ui/UIAssets';
import { AxonDateRangePicker, AxonToggleGroup, AxonToggleGroupItem } from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

const disabledDate = {
  after: new Date(),
};

const TimeRange = () => {
  const { activeTab, setTimeRange } = useTabStore(
    useShallow((state) => ({
      activeTab: state.activeTab,
      setTimeRange: state.setTimeRange,
    })),
  );

  const startDate: Date | null = get(activeTab, 'timeRangeSelected.startDate', null);
  const endDate: Date | null = get(activeTab, 'timeRangeSelected.endDate', null);

  const handleSetTimeRange = (value: EUnit) => {
    if (!value) {
      return; // do not give user to unselect
    }
    const result: ETimeRange = {
      unit: value,
      startDate: getDayFromTodayByDays(value)?.toDate() || null,
      endDate: getDateFromToday(0)?.toDate() || null,
    };
    if (value === EUnit.CUSTOM) {
      result.startDate = startDate;
      result.endDate = endDate;
    }
    setTimeRange(result);
  };

  const handleSetCustomTimeRange = (value?: DateRange) => {
    setTimeRange({
      unit: EUnit.CUSTOM,
      startDate: getDayjsFromDate(value?.from).toDate(),
      endDate: getDayjsFromDate(value?.to).toDate() || new Date(),
    });
  };

  const timeRange = get(activeTab, 'timeRangeSelected.unit', EUnit.THREE_DAYS);

  return (
    <div className='flex flex-row items-center gap-x-3'>
      <div>
        <AxonToggleGroup type='single' value={timeRange} onValueChange={handleSetTimeRange}>
          <AxonToggleGroupItem className='' value={EUnit.CUSTOM}>
            <AxonDateRangePicker
              placeholder={
                <div className='text-content-tertiary flex items-center gap-x-2'>
                  <span>Custom range</span>
                  <Clock className='text-content-tertiary size-4' />
                </div>
              }
              selected={{
                from: startDate ?? undefined,
                to: endDate ?? undefined,
              }}
              classNames={{
                button: 'w-fit border-none bg-inherit',
              }}
              disabled={disabledDate}
              onSelect={handleSetCustomTimeRange}
              showCustom={false}
              showIcon={false}
            />
          </AxonToggleGroupItem>
          <AxonToggleGroupItem value={EUnit.THREE_DAYS}>3 days</AxonToggleGroupItem>
          <AxonToggleGroupItem value={EUnit.SEVEN_DAYS}>7 days</AxonToggleGroupItem>
          <AxonToggleGroupItem value={EUnit.FIFTEEN_DAYS}>15 days</AxonToggleGroupItem>
        </AxonToggleGroup>
      </div>
    </div>
  );
};

export default TimeRange;
