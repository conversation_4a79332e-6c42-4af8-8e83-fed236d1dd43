import { cn } from '@/utils';
import { CornerDownLeft } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { BoldHome, getDeviceImage } from 'ui/UIAssets';
import { SearchResult } from './search.store';

type SearchItemProps = {
  result: SearchResult;
  onSelect?: (result: SearchResult) => void;
  isSelected?: boolean;
};
const SearchItem = ({ result, onSelect, isSelected }: SearchItemProps) => {
  const { t } = useTranslation();
  return (
    <div
      onClick={() => onSelect?.(result)}
      className={cn(
        'group relative',
        'hover:bg-surface-tile flex cursor-pointer flex-row items-center gap-x-3 rounded-md p-2 pr-4 transition-all',
        isSelected ? 'bg-surface-tile' : '',
      )}>
      <div className={cn('rounded-xs bg-surface-action flex size-8 items-center justify-center border')}>
        {result.type === 'customer' && (
          <div className='flex size-full items-center justify-center'>
            <BoldHome className='text-content-tertiary' />
          </div>
        )}
        {result.type === 'device' && <img src={getDeviceImage(result.deviceType || '')} alt='Device Image' />}
      </div>
      <div>
        <p className={cn('text-sm font-medium')}>{result.name || result.id}</p>
        <div className='flex items-center gap-x-1.5'>
          <p className={cn('text-content-tertiary font-book text-xs')}>
            <span className='capitalize'>
              {result.type === 'device' && result.deviceType}
              {result.type === 'customer' && t('customer')}
            </span>
            {result.active === null ? (
              ''
            ) : (
              <span className='lowercase'>, {result.active ? t('active') : t('inactive')}</span>
            )}
          </p>
        </div>
      </div>
      <div
        className={cn(
          'absolute right-4 top-4',
          'bg-surface-action h-5 rounded-[4px] border px-2 py-1 opacity-0 shadow transition-all',
          'group-hover:opacity-100',
          isSelected ? 'opacity-100' : '',
        )}>
        <CornerDownLeft size={9} />
      </div>
    </div>
  );
};

export default SearchItem;
