import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';

export type SearchResult = {
  id: string;
  type: 'customer' | 'device';
  name: string;
  deviceType: null | string;
  deviceId: string | null;
  customerId: string | null;
  active: boolean | null;
};

interface SearchStore {
  recentSearchResults: SearchResult[];
  addRecentSearchResults: (result: SearchResult) => void;
}

const useSearchStore = create<SearchStore>()(
  devtools(
    persist(
      immer((set) => ({
        recentSearchResults: [],
        addRecentSearchResults: (result) => {
          set(
            (state) => {
              const exists = state.recentSearchResults.some((r) => r.id === result.id && r.type === result.type);

              if (!exists) {
                state.recentSearchResults.unshift(result);
              }

              state.recentSearchResults = state.recentSearchResults.slice(0, 20);
            },
            undefined,
            'addRecentSearchResults',
          );
        },
      })),
      {
        name: 'csa-SearchStore',
        storage: createJSONStorage(() => localStorage),
      },
    ),
    { name: 'csaStore' },
  ),
);

export default useSearchStore;
