import useTabStore from '@/stores/tab.store';
import { cn } from '@/utils';
import {
  ColumnDef,
  ExpandedState,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, ChevronRight, Ellipsis, Search } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetDataModel } from 'services/Customer';
import { getDeviceImage, Server2, Signifier } from 'ui/UIAssets';
import {
  AxonButton,
  AxonImage,
  AxonSheet,
  AxonSheetContent,
  AxonSheetTrigger,
  AxonTableData,
  AxonTableInputSearch,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuItem,
  AxonDropdownMenuTrigger,
} from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';

type Device = {
  deviceId: string;
  type: string;
  children?: Device[];
};

type IndicesExpanded = Record<string, Record<string, true>>;

function findCPEIndicesExpanded(
  devices: Device[],
  path: string = '',
  cpeIndices: IndicesExpanded = {},
): IndicesExpanded {
  devices.forEach((device, idx) => {
    const currentPath = path ? `${path}.${idx}` : `${idx}`;

    if (device.type === 'CPE') {
      const parts = currentPath.split('.');
      const expanded: Record<string, true> = {};
      let partial = '';
      for (const part of parts) {
        partial = partial ? `${partial}.${part}` : part;
        expanded[partial] = true;
      }
      cpeIndices[device.deviceId] = expanded;
    }

    if (device.children && device.children.length > 0) {
      findCPEIndicesExpanded(device.children, currentPath, cpeIndices);
    }
  });

  return cpeIndices;
}

const DataModelDrawer = () => {
  const { t } = useTranslation();
  const customerId = useTabStore((state) => state.activeTab?.customerId);
  const deviceId = useTabStore((state) => state.activeTab?.deviceId);
  const { setActiveTab, addTab } = useTabStore(
    useShallow((state) => ({
      setActiveTab: state.setActiveTab,
      addTab: state.addTab,
    })),
  );

  const { data: dataModel } = useGetDataModel(customerId || '', { enabled: !!customerId });
  const data = useMemo(() => dataModel?.data?.result || [], [dataModel]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [expanded, setExpanded] = useState<ExpandedState>(true);

  const cpeIndices = useMemo(() => findCPEIndicesExpanded(data), [data]);
  const cpeIndex = useMemo(() => {
    return deviceId ? cpeIndices[deviceId] : {};
  }, [cpeIndices, deviceId]);

  useEffect(() => {
    setExpanded(cpeIndex);
  }, [cpeIndex]);

  const goToCpePage = useCallback(
    (deviceId: string) => {
      if (!customerId) return;
      addTab({
        id: deviceId,
        name: deviceId,
        type: 'device',
        deviceId: deviceId,
        customerId: customerId,
      });
      setActiveTab({
        tabId: deviceId,
        tabType: 'device',
      });
    },
    [customerId, addTab, setActiveTab],
  );

  const columns = useMemo<ColumnDef<(typeof data)[0], any>[]>(
    () => [
      {
        accessorKey: 'deviceId',
        enableSorting: false,
        header: ({ table }) => (
          <div className='flex items-center gap-x-2'>
            <AxonButton onClick={table.getToggleAllRowsExpandedHandler()} variant='ghost' size='icon'>
              {table.getIsAllRowsExpanded() ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
            </AxonButton>
            Device ID
          </div>
        ),
        cell: ({ row, getValue }) => (
          <div
            style={{
              paddingLeft: `${row.depth * 2.5}rem`,
            }}>
            <div className={cn('flex items-center gap-x-2')}>
              {row.getCanExpand() ? (
                <AxonButton onClick={row.getToggleExpandedHandler()} variant='ghost' size='icon'>
                  {row.getIsExpanded() ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                </AxonButton>
              ) : (
                <div className='w-8'></div>
              )}
              <AxonImage
                src={getDeviceImage(row.original.deviceType)}
                size={'xs'}
                className='p-1'
                label={row.original.type === 'CPE' ? 'axon' : undefined}
                alt='Data Model Image'
              />
              <div className='flex flex-col'>
                <p className={getValue() === deviceId ? 'font-bold' : ''}>{getValue()}</p>
                <div className='flex items-center gap-1'>
                  {row.original.status === 'Online' ? (
                    <Signifier className='text-content-meta-green' />
                  ) : (
                    <Signifier className='text-content-disabled' />
                  )}
                  <span className='text-content-tertiary text-xs capitalize'>{row.original.status}</span>
                </div>
              </div>
            </div>
          </div>
        ),
      },
      { accessorKey: 'type', header: 'CPE/Client', cell: (info) => info.getValue() },
      { accessorKey: 'deviceType', header: 'Device Type', cell: (info) => info.getValue() },
      { accessorKey: 'model', header: 'Device Model', cell: (info) => info.getValue() },
      {
        id: 'more',
        header: '',
        cell: ({ row }) =>
          row.original.type === 'CPE' ? (
            <AxonDropdownMenu modal={false}>
              <AxonDropdownMenuTrigger asChild>
                <AxonButton variant='outline' size='icon'>
                  <Ellipsis size={16} />
                </AxonButton>
              </AxonDropdownMenuTrigger>
              <AxonDropdownMenuContent>
                <AxonDropdownMenuItem onClick={() => goToCpePage(row.original.deviceId)}>
                  {t('devicesDrawer.open_this_cpe')}
                </AxonDropdownMenuItem>
              </AxonDropdownMenuContent>
            </AxonDropdownMenu>
          ) : null,
      },
    ],
    [deviceId, goToCpePage, t],
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      globalFilter,
      expanded: expanded,
    },
    onExpandedChange: setExpanded,

    getSubRows: (row) => row.children,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSortedRowModel: getSortedRowModel(),
    filterFromLeafRows: true,
  });

  const handleFilterChange = useCallback((value: string) => {
    setGlobalFilter(String(value));
  }, []);

  return (
    <AxonSheet>
      <AxonSheetTrigger asChild>
        <a className='flex items-center gap-1 px-0 py-2'>
          <Server2 className='text-component-hyperlink size-5' />
          <p className='text-component-hyperlink text-xs font-medium'>{t('sidebar.deviceList.viewDataModel')}</p>
        </a>
      </AxonSheetTrigger>
      <AxonSheetContent className='max-w-5xl p-0' onClick={(e) => e.stopPropagation()}>
        <div className='flex h-full flex-col'>
          <p className='border-b-border-flat h-16 border-b px-6 py-5 text-xl font-medium'>
            Devices <span className='text-content-tertiary font-normal'>{table.getRowCount()}</span>
          </p>
          <div className='border-b-border-flat insight-search-block flex items-center justify-between border-b px-6 py-4'>
            <div className='relative h-fit'>
              <Search className='text-content-tertiary absolute left-3 top-1/2 size-4 -translate-y-1/2' />
              <AxonTableInputSearch
                value={globalFilter}
                onChange={handleFilterChange}
                placeholder={'Search by device ID and model name'}
                className='w-80'
              />
            </div>
          </div>
          <div className='scrollbar-lg bg-surface-section text-content-primary/75 flex-1 overflow-auto'>
            <AxonTableData table={table} showFooter={false} />
          </div>
        </div>
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default DataModelDrawer;
