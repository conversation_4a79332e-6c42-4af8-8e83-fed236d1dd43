import React, { useState } from 'react';
import { getRelativeTimeFromNow } from 'services/Utils';
import { Clock } from 'ui/UIAssets';
import { useInterval } from 'services/Hooks';
import { useTranslation } from 'react-i18next';
type TimeTextProps = {
  unixTimeMiliSecs: number;
};
const TimeText = ({ unixTimeMiliSecs }: TimeTextProps) => {
  const { t } = useTranslation();
  const [, setNow] = useState(Date.now());

  useInterval(() => {
    setNow(Date.now());
  }, 60 * 1000);

  return (
    <p className='text-content-tertiary text-2xs font-book flex items-center justify-center'>
      <Clock className='mr-1 size-4' /> {t('sidebar.deviceList.retrievedOn')} {getRelativeTimeFromNow(unixTimeMiliSecs)}
    </p>
  );
};

export default TimeText;
