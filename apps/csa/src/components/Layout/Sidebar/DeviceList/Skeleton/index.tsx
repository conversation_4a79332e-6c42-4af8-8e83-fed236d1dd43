import { AxonSeparator, AxonSkeletonLoader } from 'ui/UIComponents';

const Skeleton = () => {
  return (
    <>
      <div className='h-[257px]'></div>
      <AxonSeparator />
      {[1, 2, 3, 4].map((v) => (
        <>
          <div key={v} className='flex flex-row items-center gap-x-2 p-4'>
            <AxonSkeletonLoader className='size-[38px]' />
            <div className='flex flex-col gap-1'>
              <AxonSkeletonLoader className='h-[18px] w-[120px]' />
              <AxonSkeletonLoader className='h-[14px] w-[60px]' />
            </div>
          </div>
          <AxonSeparator />
        </>
      ))}
    </>
  );
};

export default Skeleton;
