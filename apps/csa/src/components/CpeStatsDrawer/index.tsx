import useTabStore, { EUnit } from '@/stores/tab.store';
import { cn } from '@/utils';
import { convertQOEStatus } from '@/utils/QOE.util';
import { ColumnDef, getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { Search } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useQueryGetCpeStatsDrawer } from 'services/CPEService';
import { useGetSidebarTopology } from 'services/Topology';
import { getUnixTime } from 'services/Utils';
import { BoldDangerCircle } from 'ui/UIAssets';
import {
  AxonDateRangePicker,
  AxonInput,
  AxonSelectWrapper,
  AxonSheet,
  AxonSheetContent,
  AxonTableData,
} from 'ui/UIComponents';

const disabledDate = {
  after: new Date(),
  before: new Date(new Date().setMonth(new Date().getMonth() - 1)),
};

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

type CpeStatsDrawerProps = {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
};

const CpeStatsDrawer = ({ open, onOpenChange }: CpeStatsDrawerProps) => {
  const activeTab = useTabStore((state) => state.activeTab);
  const setTimeRange = useTabStore((state) => state.setTimeRange);

  const customerId = activeTab?.customerId || '';
  const deviceId = activeTab?.deviceId || '';
  const startDate = activeTab?.timeRangeSelected?.startDate;
  const endDate = activeTab?.timeRangeSelected?.endDate;

  const [searchedDeviceType, setSearchedDeviceType] = useState<string>('');
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>(deviceId);

  const { data } = useGetSidebarTopology(customerId, {
    enabled: !!customerId,
  });

  const devices = useMemo(() => {
    if (data && data.data && Array.isArray(data.data.devices)) {
      return data.data.devices;
    }

    return [];
  }, [data]);

  const { data: queryData } = useQueryGetCpeStatsDrawer(
    {
      customerId,
      deviceId: selectedDeviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
    },
    { enabled: !!customerId && !!selectedDeviceId && !!startDate && !!endDate },
  );
  const QOE = queryData?.data?.QOEStatus;

  const parameters = useMemo(() => {
    if (queryData && queryData.data) {
      const parameters = Array.isArray(queryData?.data?.parameters) ? queryData?.data?.parameters : [];

      if (QOE) {
        return [
          {
            type: QOE.type,
            minMax: null,
            average: null,
            unit: '',
            latestResult: convertQOEStatus(QOE.latestResult),
            hasWarning: null,
            noOfTest: null,
          },
          ...parameters,
        ];
      }

      return parameters;
    }

    return [];
  }, [QOE, queryData]);

  const handleSetCustomTimeRange = (value?: DateRange) => {
    setTimeRange({
      unit: EUnit.CUSTOM,
      startDate: value?.from || null,
      endDate: value?.to || null,
    });
  };

  const filteredParameters = useMemo(() => {
    if (searchedDeviceType) {
      return parameters.filter((p) => p.type.toLowerCase().includes(searchedDeviceType.toLowerCase()));
    }

    return parameters;
  }, [parameters, searchedDeviceType]);

  const columns = useMemo<ColumnDef<NonNullable<typeof filteredParameters>[number], any>[]>(
    () => [
      {
        header: 'Type',
        accessorKey: 'type',
        cell: ({ row }) => {
          const { type } = row.original;
          return <span className='text-md text-content-primary font-medium'>{type}</span>;
        },
      },
      {
        header: 'Min-Max',
        accessorKey: `minMax`,
        cell: ({ row }) => {
          const { minMax, unit } = row.original;
          return <span className='text-md text-content-primary font-normal'>{minMax ? `${minMax} ${unit}` : '-'}</span>;
        },
      },
      {
        header: 'Average',
        accessorKey: 'average',
        cell: ({ row }) => {
          const { average, unit } = row.original;
          return (
            <span className='text-md text-content-primary font-normal'>
              {typeof average !== 'undefined' && average !== null ? `${average} ${unit}` : '-'}
            </span>
          );
        },
      },
      {
        header: 'Latest result',
        accessorKey: 'latestResult',
        cell: ({ row }) => {
          const { latestResult, unit, hasWarning } = row.original;

          return (
            <div className='flex flex-col items-start gap-2'>
              <span className='text-content-primary'>
                {typeof latestResult !== 'undefined' && latestResult !== null ? `${latestResult} ${unit}` : '-'}
              </span>
              {hasWarning && (
                <div className='flex items-center gap-1'>
                  <BoldDangerCircle color='rgb(var(--content-meta-red))' />
                  <span className='text-content-meta-red text-xs font-medium'>Warning</span>
                  <span className='font-book text-xs opacity-60'>too high</span>
                </div>
              )}
            </div>
          );
        },
      },
      {
        header: 'No. of tests',
        accessorKey: 'noOfTest',
        cell: ({ row }) => {
          const { noOfTest } = row.original;
          return <span className='text-md text-content-primary font-normal'>{noOfTest}</span>;
        },
      },
    ],
    [],
  );

  const table = useReactTable({
    columns,
    data: filteredParameters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return (
    <AxonSheet open={open} onOpenChange={onOpenChange}>
      <AxonSheetContent
        className={cn(
          'insight-axonsheet-content lg w-full overflow-y-auto p-0 sm:max-w-5xl md:max-w-screen-lg 2xl:max-w-screen-xl',
        )}>
        <div className='flex h-screen flex-col'>
          <div className={cn('border-b-border-flat h-16 border-b px-4 py-5 text-lg font-medium')}>
            <AxonSelectWrapper
              classes={{
                selectTrigger: 'gap-xs w-fit border text-xl hover:bg-accent',
              }}
              value={selectedDeviceId}
              onChange={setSelectedDeviceId}
              options={devices.map((d) => {
                return {
                  label: `CPE statistics - ${d.deviceUniqueId}`,
                  value: d.deviceUniqueId,
                };
              })}
            />
          </div>
          <div className={cn('border-b-border-flat flex items-center justify-between border-b p-4')}>
            <div className='relative w-96'>
              <Search size={16} className='text-muted-foreground absolute left-3 top-3' />
              <AxonInput
                placeholder='Search by type'
                className='pl-10'
                value={searchedDeviceType}
                onChange={(e) => setSearchedDeviceType(e.target.value)}
              />
            </div>
            <div className='ml-auto flex items-center gap-x-2'>
              <AxonDateRangePicker
                selected={{
                  from: activeTab?.timeRangeSelected?.startDate ?? undefined,
                  to: activeTab?.timeRangeSelected?.endDate ?? undefined,
                }}
                disabled={disabledDate}
                onSelect={handleSetCustomTimeRange}
                showIcon={false}
              />
            </div>
          </div>

          <div className={cn('bg-surface-section h-full')}>
            <AxonTableData table={table} showFooter={false} />
          </div>
        </div>
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default CpeStatsDrawer;
