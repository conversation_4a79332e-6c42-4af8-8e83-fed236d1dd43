import { useChatbotStore } from '../chatbotStore';
import { getWsMessage } from '../utils';
import { useChatbotWs } from './useChatbotWs';

export const useSendUserMessageToWS = () => {
  const { sendJsonMessage } = useChatbotWs();

  const sendChatbotMessage = useChatbotStore.use.sendMessage();
  const sendLoadingMessage = useChatbotStore.use.sendLoadingMessage();
  return (content: string) => {
    sendChatbotMessage({ owner: 'user', content });
    sendLoadingMessage();
    sendJsonMessage(getWsMessage(content));
  };
};
