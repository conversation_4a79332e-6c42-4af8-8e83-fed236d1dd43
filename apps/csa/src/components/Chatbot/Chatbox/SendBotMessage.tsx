import { useCallback, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useChatbotStore } from '../chatbotStore';
import { useChatbotWs } from '../hooks/useChatbotWs';

const SendBotMessage = () => {
  const { lastJsonMessage } = useChatbotWs();
  const sendChatbotMessage = useChatbotStore.use.sendMessage();
  const updateChatbotMessage = useChatbotStore.use.updateMessage();
  const removeLoadingMessage = useChatbotStore.use.removeLoadingMessage();

  const botMessageIdRef = useRef<string | null>(null);
  const characterQueueRef = useRef<string[]>([]);
  const displayedContentRef = useRef<string>(''); // This contains the current content of the message and is updated in setInterval
  const typingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const TYPING_SPEED = 10;
  const CHARACTER_PER_INTERVAL = 5;

  const startTypingLoop = useCallback(() => {
    // this condition prevents multiple setInterval timers from running at the same time
    // because each new chunk will trigger this function
    if (typingIntervalRef.current) return;

    typingIntervalRef.current = setInterval(() => {
      if (characterQueueRef.current.length === 0) return;

      const nextChars = characterQueueRef.current.splice(0, CHARACTER_PER_INTERVAL).join('');
      if (!nextChars) return;

      displayedContentRef.current += nextChars;

      if (botMessageIdRef.current) {
        updateChatbotMessage({
          id: botMessageIdRef.current,
          content: displayedContentRef.current,
        });
      }
    }, TYPING_SPEED);
  }, [updateChatbotMessage]);

  const stopTypingLoop = useCallback(() => {
    if (typingIntervalRef.current) {
      clearInterval(typingIntervalRef.current);
      typingIntervalRef.current = null;
    }
  }, []);

  useEffect(() => {
    if (!lastJsonMessage) return;

    const message = lastJsonMessage as {
      delta?: string;
      status: string;
    };

    if (message.delta) {
      // First delta -> init message
      if (!botMessageIdRef.current) {
        const newMessageId = uuidv4();

        removeLoadingMessage();

        sendChatbotMessage({
          id: newMessageId,
          owner: 'bot',
          content: '', // Send empty message initially
        });

        botMessageIdRef.current = newMessageId;
        displayedContentRef.current = '';
      }

      // Add characters to queue
      characterQueueRef.current.push(...message.delta.split(''));
      startTypingLoop();
    }

    if (message.status === 'complete') {
      const finishTyping = () => {
        // We need to wait for the queue to empty
        // The reason is that the queue runs slower than retrieving responses from server
        if (characterQueueRef.current.length > 0) {
          setTimeout(finishTyping, TYPING_SPEED);
          return;
        }

        // Now the queue is empty
        // Final update to ensure content is accurate
        if (botMessageIdRef.current) {
          updateChatbotMessage({
            id: botMessageIdRef.current,
            content: displayedContentRef.current,
          });
        }

        // Reset everything
        stopTypingLoop();
        botMessageIdRef.current = null;
        characterQueueRef.current = [];
        displayedContentRef.current = '';
      };

      finishTyping();
    }
  }, [
    lastJsonMessage,
    sendChatbotMessage,
    updateChatbotMessage,
    removeLoadingMessage,
    startTypingLoop,
    stopTypingLoop,
  ]);

  return null;
};

export default SendBotMessage;
