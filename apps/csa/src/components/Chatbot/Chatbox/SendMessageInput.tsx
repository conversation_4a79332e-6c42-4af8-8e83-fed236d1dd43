import { Send } from 'lucide-react';
import { KeyboardEventHandler, useEffect, useRef, useState } from 'react';
import { ReadyState } from 'react-use-websocket';
import { AxonButton, AxonTextarea } from 'ui/UIComponents';
import { useChatbotStore } from '../chatbotStore';
import { useChatbotWs } from '../hooks/useChatbotWs';
import { useSendUserMessageToWS } from '../hooks/useUserSendMessage';
const SendMessageInput = () => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const sendUserMessageToWS = useSendUserMessageToWS();
  const isWaitingResponse = useChatbotStore.use.isWaiting();
  const [value, setValue] = useState('');
  const { readyState } = useChatbotWs();

  const isInputEnabled = !isWaitingResponse && readyState === ReadyState.OPEN;

  const handleSubmit = (message: string) => {
    if (!isInputEnabled || !message) return;
    sendUserMessageToWS(message);
    setValue('');
  };

  const handleKeyDown: KeyboardEventHandler<HTMLTextAreaElement> | undefined = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSubmit(value);
    }
  };

  const adjustHeight = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    textarea.style.height = '48px'; // Reset height
    textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`; // Adjust height based on content
  };

  useEffect(() => {
    adjustHeight(); // Adjust height on mount (useful if there's initial value)
    if (textareaRef.current && isInputEnabled) {
      textareaRef.current.focus();
    }
  }, [isInputEnabled, value]);

  return (
    <div className='relative flex flex-col'>
      <div className='relative flex h-12 w-full items-end'>
        <AxonTextarea
          value={value}
          ref={textareaRef}
          placeholder={isWaitingResponse ? 'Thinking...' : 'Message...'}
          onChange={(e) => setValue(e.target.value)}
          onInput={adjustHeight} // Adjust height dynamically
          onKeyDown={handleKeyDown}
          disabled={!isInputEnabled}
          className='text-md min-h-none scrollbar-sm absolute bottom-0 h-12 max-h-[200px] resize-none overflow-y-auto pr-10'
        />
      </div>
      <AxonButton
        disabled={!isInputEnabled}
        className='absolute right-2 top-2'
        variant='primary'
        onClick={() => handleSubmit(value)}>
        <Send className='size-4' />
      </AxonButton>
    </div>
  );
};

export default SendMessageInput;
