import { useTranslation } from 'react-i18next';

export default function InsightsNoData() {
  const { t } = useTranslation();
  return (
    <div className='relative'>
      <div className='bg-surface-action--hover border-border absolute -top-2 h-40 w-full scale-x-[85%] rounded-sm border'></div>
      <div className='bg-surface-action--hover border-border absolute -top-1 h-40 w-full scale-x-[95%] rounded-sm border'></div>
      <div className='bg-surface-action--hover w-304px border-border relative flex h-[344px] items-center justify-center rounded-sm border'>
        <p className='text-content-tertiary font-book'>{t('insights.noInsight')}</p>
      </div>
    </div>
  );
}
