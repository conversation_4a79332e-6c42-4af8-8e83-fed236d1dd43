export enum EInsightPriority {
  HIGH = 'HIGH',
  MODERATE = 'MODERATE',
  LOW = 'LOW',
}

export enum EInsightType {
  ALERT = 'alert',
  RECOMMENDATION = 'recommendation',
}

export enum EInsightSeverity {
  INFO = 'INFO',
  WARN = 'WARN',
  ACTION = 'ACTION',
}

export enum EInsightCategoryType {
  AP = 'AP',
  INTERFACE = 'INTERFACE',
  STATION = 'STATION',
}

export const priorityLabelMap: Record<string, string> = {
  HIGH: 'Critical',
  MODERATE: 'Major',
  LOW: 'Minor',
};
