import { EInsightType } from '@/components/Insights/insight.enum';
import { IInsight } from '@/components/Insights/insight.type';
import { cn } from '@/utils';

interface IProps {
  card: IInsight;
}

export const InsightsTrend = ({ card }: IProps) => {
  const { type } = card;
  return (
    <div className='flex flex-1 items-center justify-end gap-1'>
      <div
        className={cn(
          type === EInsightType.ALERT ? 'bg-content-insights-alert' : 'bg-content-insights-recommendation',
          'rounded-2xs h-[12px] w-[6px] opacity-50',
        )}></div>
      <div
        className={cn(
          type === EInsightType.ALERT ? 'bg-content-insights-alert' : 'bg-content-insights-recommendation',
          'rounded-2xs h-[12px] w-[6px]',
        )}></div>
      <div
        className={cn(
          type === EInsightType.ALERT ? 'bg-content-insights-alert' : 'bg-content-insights-recommendation',
          'rounded-2xs h-[12px] w-[6px]',
        )}></div>
      <div
        className={cn(
          type === EInsightType.ALERT ? 'bg-content-insights-alert' : 'bg-content-insights-recommendation',
          'rounded-2xs h-[12px] w-[6px] opacity-50',
        )}></div>
      <div
        className={cn(
          type === EInsightType.ALERT ? 'bg-content-insights-alert' : 'bg-content-insights-recommendation',
          'rounded-2xs h-[12px] w-[6px]',
        )}></div>
      <div
        className={cn(
          type === EInsightType.ALERT ? 'bg-content-insights-alert' : 'bg-content-insights-recommendation',
          'rounded-2xs h-[12px] w-[6px] opacity-50',
        )}></div>
      <div
        className={cn(
          type === EInsightType.ALERT ? 'bg-content-insights-alert' : 'bg-content-insights-recommendation',
          'rounded-2xs h-[12px] w-[28px]',
        )}></div>
    </div>
  );
};
