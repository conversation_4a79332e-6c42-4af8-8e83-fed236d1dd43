import { Search } from 'ui/UIAssets';
import {
  AxonButton,
  AxonDialog,
  AxonDialogClose,
  AxonDialogContent,
  AxonDialogHeader,
  AxonDialogTitle,
  AxonDialogTrigger,
  AxonLabel,
} from 'ui/UIComponents';
import { IInsight } from '../insight.type';
import { EInsightType } from '../insight.enum';
import { formatDate } from 'services/Utils';
import InsightsPriority from '@/components/Insights/InsightsPriority';
import useTabStore from '@/stores/tab.store';
import { useShallow } from 'zustand/react/shallow';
import { useTranslation } from 'react-i18next';
interface IProps {
  data: IInsight;
}

export const DetailDialog = ({ data }: IProps) => {
  const { t } = useTranslation();
  const { setActiveTab, addTab, activeTab } = useTabStore(
    useShallow((state) => ({
      setActiveTab: state.setActiveTab,
      addTab: state.addTab,
      activeTab: state.activeTab,
    })),
  );
  const customerId = activeTab?.customerId;
  const { type, insightName, recommendation, diagnostic, priority, occurrenceDay, lineId } = data;

  const weeklyTrend = ['disabled', 'enabled', 'enabled', 'disabled', 'enabled', 'disabled', 'enabled'];

  const goToCpePage = () => {
    addTab({
      id: lineId,
      name: lineId,
      type: 'device',
      deviceId: lineId,
      customerId: customerId,
    });
    setActiveTab({
      tabId: lineId,
      tabType: 'device',
    });
  };

  return (
    <AxonDialog>
      <AxonDialogTrigger>
        <AxonButton
          variant='ghost'
          className={`${type === EInsightType.ALERT ? 'bg-component-alert-tile-accent hover:bg-yellow-500/75 active:bg-yellow-500/50' : 'bg-component-rcmd-tile-accent hover:bg-blue-500/75 active:bg-blue-500/50'} text-neutral-0 font-book text-xs shadow-md`}>
          <Search />
          {t('insights.detail.viewDetail')}
        </AxonButton>
      </AxonDialogTrigger>
      <AxonDialogContent>
        <AxonDialogHeader>
          <AxonDialogTitle>
            <p className='text-content-primary font-global text-lg font-medium'>{t('insights.detail.viewInsight')}</p>
          </AxonDialogTitle>
        </AxonDialogHeader>
        <div className='flex flex-col gap-4'>
          <div>
            <p className='text-content-secondary font-book text-sm'>{t('insights.occurred')}</p>
            <p className='text-content-primary text-md p-3'>{formatDate(occurrenceDay, 'MMMM DD, YYYY')}</p>
          </div>
          <div>
            <p className='text-content-secondary font-book text-sm'>{t('insights.detail.type')}</p>
            <p className='text-content-primary text-md p-3 capitalize'>{type}</p>
          </div>
          {type === EInsightType.ALERT && (
            <div className='relative'>
              <p className='text-content-secondary font-book text-sm'>{t('insights.detail.severity')}</p>
              <div className='flex items-center gap-x-1 p-3'>
                <InsightsPriority priority={priority} />
              </div>
            </div>
          )}
          <div>
            <p className='text-content-secondary font-book text-sm'>{t('insights.detail.title')}</p>
            <p className='text-content-primary text-md p-3 capitalize'>{insightName}</p>
          </div>
          <div>
            <p className='text-content-secondary font-book text-sm'>{t('insights.detail.detail')}</p>

            <div className='text-content-secondary p-3'>
              <span>{recommendation || diagnostic}</span>
            </div>
          </div>
          <div>
            <AxonLabel className='text-content-secondary text-sm'>{t('insights.detail.weeklyTrend')}</AxonLabel>
            <div className='flex w-fit flex-col gap-2'>
              <div className='mt-3 flex flex-row gap-2'>
                {weeklyTrend.map((data, index) => (
                  <>
                    {index === 6 ? (
                      <span
                        key={index}
                        className={`rounded-2xs h-4 w-10 border border-solid ${data === 'enabled' ? (type === EInsightType.ALERT ? 'bg-content-meta-orange' : 'bg-component-rcmd-tile-accent') : ''}`}></span>
                    ) : (
                      <span
                        key={index}
                        className={`size-4 rounded-[3px] border border-solid ${data === 'enabled' ? (type === EInsightType.ALERT ? 'bg-content-meta-orange' : 'bg-component-rcmd-tile-accent') : ''}`}></span>
                    )}
                  </>
                ))}
              </div>
              <div className='text-content-tertiary flex flex-row justify-between'>
                <span>16 Apr</span>
                <span>Today</span>
              </div>
            </div>
          </div>
          <div className='flex flex-row justify-end'>
            <AxonDialogClose>
              <AxonButton variant='primary' onClick={goToCpePage}>
                {t('insights.detail.goToCpePage')}
              </AxonButton>
            </AxonDialogClose>
          </div>
        </div>
      </AxonDialogContent>
    </AxonDialog>
  );
};
