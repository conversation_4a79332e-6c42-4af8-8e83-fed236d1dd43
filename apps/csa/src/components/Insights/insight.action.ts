import get from 'lodash/get';
import { useQueryGetInsight } from 'services/Insight';
import { getUnixTime } from 'services/Utils';
import useTabStore, { EUnit } from '@/stores/tab.store';
import { useShallow } from 'zustand/react/shallow';
import { useMemo, useState } from 'react';
import { IInsight } from './insight.type';
import { useGetCpeInfo } from 'services/CPEService';
import { ALL_VALUE_OPTION } from './InsightsDrawer';
import debounce from 'lodash/debounce';

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

export const useInsightAction = () => {
  const { activeTab, setTimeRange } = useTabStore(
    useShallow((state) => ({
      activeTab: state.activeTab,
      setTimeRange: state.setTimeRange,
    })),
  );
  const lineId = activeTab?.deviceId || activeTab?.customerId;
  const isDeviceTab = activeTab?.type === 'device';
  const { data: cpeInfo, isLoading: isCpeInfoLoading } = useGetCpeInfo(lineId ?? '', {
    enabled: !!lineId,
    staleTime: Infinity,
  });
  const customerId = get(cpeInfo, 'data.customerId') ?? ''; // id of account
  const activeTabDeviceId = get(cpeInfo, 'data.id') ?? ''; // id of cpe

  const [filterQueryParams, setFilterQueryParams] = useState({
    search: '',
    deviceId: '',
    category: '',
    severity: '',
    sortTime: 'newest',
    type: '',
  });

  const startDate = get(activeTab, 'timeRangeSelected.startDate', null);
  const endDate = get(activeTab, 'timeRangeSelected.endDate', null);
  const {
    data: response,
    isLoading: isInsightLoading,
    ...rest
  } = useQueryGetInsight(
    {
      customerId,
      deviceId: isDeviceTab ? activeTabDeviceId : undefined,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    { enabled: Boolean(customerId) && Boolean(startDate), staleTime: Infinity },
  );

  const insights = useMemo(() => {
    const data: IInsight[] = get(response, 'data.insights') || [];
    return data.sort((a, b) => {
      return b.occurrenceDay - a.occurrenceDay; // sort by occurrenceDay descending
    });
  }, [response]);

  const debouncedSearchInput = useMemo(() => {
    return debounce((value: string) => {
      setFilterQueryParams({ ...filterQueryParams, search: value });
    }, 500);
  }, [setFilterQueryParams, filterQueryParams]);

  const filteredData = useMemo(() => {
    const { deviceId: deviceIdQuery, category, severity, type, search, sortTime } = filterQueryParams;
    const result = insights.filter((item) => {
      const filterByDeviceId =
        deviceIdQuery && deviceIdQuery !== ALL_VALUE_OPTION ? item.lineId === deviceIdQuery : true;
      const filterByTabId = isDeviceTab ? item.lineId === activeTabDeviceId : true;
      const filterByCategory = category && category !== ALL_VALUE_OPTION ? item.categoryId === category : true;
      const filterBySeverity = severity && severity !== ALL_VALUE_OPTION ? item.priority === severity : true;
      const filterByType = type && type !== ALL_VALUE_OPTION ? item.type === type : true;
      const filterBySearch = search
        ? item.insightName.toLowerCase().includes(search.toLowerCase()) ||
          item.diagnostic.toLowerCase().includes(search.toLowerCase())
        : true;
      return (
        filterByDeviceId && filterByCategory && filterBySeverity && filterByType && filterBySearch && filterByTabId
      );
    });
    return result.sort((a, b) => {
      if (sortTime === 'newest') {
        return b.occurrenceDay - a.occurrenceDay;
      }
      return a.occurrenceDay - b.occurrenceDay;
    });
  }, [filterQueryParams, insights, isDeviceTab, activeTabDeviceId]);

  function handleFilter(key: string, value: string) {
    setFilterQueryParams({
      ...filterQueryParams,
      [key]: value,
    });
  }

  function handleSetTimeRange(date?: DateRange) {
    setTimeRange({
      unit: EUnit.CUSTOM,
      startDate: date?.from || null,
      endDate: date?.to || null,
    });
  }

  return {
    ...rest,
    data: response,
    insights,
    filteredData,
    filterQueryParams,
    handleFilter,
    handleSetTimeRange,
    startDate,
    endDate,
    activeTabDeviceId,
    debouncedSearchInput,
    isLoading: isCpeInfoLoading || isInsightLoading,
  };
};
