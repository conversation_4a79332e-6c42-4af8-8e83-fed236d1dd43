import { EInsightCategoryType } from './insight.enum';
export type TCardType = 'alert' | 'recommendation';

export interface IParamsInsight {
  lineId: string;
  startDate: number; // yyyyMMdd
}

export interface ChartDataPoint {
  time: number;
  value: number;
}

export interface IInsight {
  lineId: string;
  type: TCardType;
  categoryId: string;
  categoryName: string;
  categoryType: EInsightCategoryType;
  insightName: string;
  insightCode: string;
  priority: string;
  priorityName: string;
  severity: string;
  severityName: string;
  recommendation: string;
  diagnostic: string;
  occurrenceDay: number;
}

export interface InsightReponse {
  code: number;
  data: {
    insights: IInsight[];
    categoryIds: {
      categoryId: string;
      categoryName: string;
    }[];
    deviceIds: string[];
    severities: {
      severity: string;
      severityName: string;
    }[];
  } | null;
  message: string;
}
