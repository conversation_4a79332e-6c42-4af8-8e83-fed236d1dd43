import { Maximize } from 'lucide-react';
import { AxonButton, AxonSheet, AxonSheetContent, AxonSheetTrigger } from 'ui/UIComponents';
import ClientsTable from './ClientsTable';
import { useState } from 'react';
import ClientsStats from './ClientsStats';

interface IProps {
  disabled?: boolean;
}

const ClientsDrawer = (props: IProps) => {
  const { disabled = false } = props;

  const [isSummaryView, setIsSummaryView] = useState(true);
  const onButtonClick = () => setIsSummaryView((prev) => !prev);

  return (
    <AxonSheet>
      <AxonSheetTrigger disabled={disabled} asChild>
        <AxonButton className='p-2' variant='ghost' size='icon'>
          <Maximize size={1.5} className='text-content-primary size-4' />
        </AxonButton>
      </AxonSheetTrigger>
      <AxonSheetContent className='w-4/5 max-w-none p-0' hideCloseButton>
        {isSummaryView ? (
          <ClientsTable onButtonClick={onButtonClick} />
        ) : (
          <ClientsStats onButtonClick={onButtonClick} />
        )}
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default ClientsDrawer;
