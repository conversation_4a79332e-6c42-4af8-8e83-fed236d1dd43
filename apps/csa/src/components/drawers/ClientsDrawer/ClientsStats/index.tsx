import RotateCcw from '@/pages/Device/ClientDevice/icons/RotateCcw';
import { ColumnDef, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useMemo } from 'react';
import { BoldSquareArrowUp, MoreHorizontal, Repeat, Search, TrendingUp, X } from 'ui/UIAssets';
import {
  AxonButton,
  AxonInput,
  AxonPopover,
  AxonPopoverContent,
  AxonPopoverTrigger,
  AxonSelect,
  AxonSelectContent,
  AxonSelectItem,
  AxonSelectTrigger,
  AxonSelectValue,
  AxonSeparator,
  AxonSheetClose,
  AxonTableData,
} from 'ui/UIComponents';
import { useTranslation } from 'react-i18next';

type NetworkTestResult = {
  type: string;
  minMax: string;
  average: string;
  latestResult: {
    value: string;
    description?: string;
  };
  numberOfTests: number;
};

type ClientsStatsProps = {
  onButtonClick?: () => void;
};

const ClientsStats = ({ onButtonClick }: ClientsStatsProps) => {
  const { t } = useTranslation();
  const columns: ColumnDef<NetworkTestResult>[] = [
    {
      accessorKey: 'type',
      header: t('customer:healthCheck.wheel.client.drawer.statistics.header.type'),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: 'minMax',
      header: t('customer:healthCheck.wheel.client.drawer.statistics.header.minMax'),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: 'average',
      header: t('customer:healthCheck.wheel.client.drawer.statistics.header.average'),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: 'latestResult',
      header: t('customer:healthCheck.wheel.client.drawer.statistics.header.latestResult'),
      cell: ({ row }) => {
        const latest = row.original.latestResult;
        return (
          <div className='flex items-center gap-x-2'>
            <div className='flex flex-col'>
              <span>{latest.value}</span>
              {latest.description && (
                <div className='flex items-center gap-x-2'>
                  <div>
                    <BoldSquareArrowUp className='text-content-meta-green size-4' />
                  </div>
                  <span className='text-xs text-gray-500'>{latest.description}</span>
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'numberOfTests',
      header: t('customer:healthCheck.wheel.client.drawer.statistics.header.numOfTest'),
      cell: (info) => info.getValue(),
    },
    {
      id: 'actions',
      cell: () => (
        <AxonPopover>
          <AxonPopoverTrigger>
            <AxonButton variant={'outline'} size={'icon'}>
              <MoreHorizontal />
            </AxonButton>
          </AxonPopoverTrigger>
          <AxonPopoverContent className='w-[200px]'>
            <div className='flex items-center gap-x-2'>
              <TrendingUp />
              <p className='text-md'>{t('customer:healthCheck.wheel.client.drawer.statistics.body.openGraph')}</p>
            </div>
          </AxonPopoverContent>
        </AxonPopover>
      ),
    },
  ];

  const data: NetworkTestResult[] = useMemo(
    () => [
      {
        type: t('customer:healthCheck.wheel.client.drawer.statistics.body.status'),
        minMax: '-',
        average: '-',
        latestResult: {
          value: 'Stable',
        },
        numberOfTests: 10,
      },
      {
        type: t('customer:healthCheck.wheel.client.drawer.statistics.body.wifiPhyRate'),
        minMax: 'xx - xx',
        average: 'xx',
        latestResult: {
          value: 'xx',
          description: 'xx% vs. SLA (xx Mbps)',
        },
        numberOfTests: 5,
      },
      {
        type: t('customer:healthCheck.wheel.client.drawer.statistics.body.wifiThroughput'),
        minMax: 'xx - xx',
        average: 'xx',
        latestResult: {
          value: 'xx',
          description: 'xx% vs. SLA (xx Mbps)',
        },
        numberOfTests: 5,
      },
      {
        type: t('customer:healthCheck.wheel.client.drawer.statistics.body.speedDn'),
        minMax: 'xx - xx',
        average: 'xx',
        latestResult: {
          value: 'xx',
          description: 'xx% vs. SLA (xx ms)',
        },
        numberOfTests: 5,
      },
      {
        type: t('customer:healthCheck.wheel.client.drawer.statistics.body.wifiLatencyDn'),
        minMax: '19 - 31 ms',
        average: '27.2 ms',
        latestResult: {
          value: '25 ms',
          description: 'xx% vs. SLA (xx ms)',
        },
        numberOfTests: 5,
      },
      {
        type: t('customer:healthCheck.wheel.client.drawer.statistics.body.wifiLatencyUp'),
        minMax: '21 - 45 ms',
        average: '35.1 ms',
        latestResult: {
          value: '21 ms',
          description: 'xx% vs. SLA (xx ms)',
        },
        numberOfTests: 5,
      },
      {
        type: t('customer:healthCheck.wheel.client.drawer.statistics.body.trafficDnHourly'),
        minMax: '0 - 194 GB',
        average: '45.2 GB',
        latestResult: {
          value: '103.7 GB',
        },
        numberOfTests: 5,
      },
      {
        type: t('customer:healthCheck.wheel.client.drawer.statistics.body.trafficUpHourly'),
        minMax: '0 - 98 GB',
        average: '11.4 GB',
        latestResult: {
          value: '29.1 GB',
        },
        numberOfTests: 5,
      },
      {
        type: t('customer:healthCheck.wheel.client.drawer.statistics.body.rssi'),
        minMax: 'xx - xx',
        average: 'xx',
        latestResult: {
          value: 'xx',
        },
        numberOfTests: 5,
      },
      {
        type: t('customer:healthCheck.wheel.client.drawer.statistics.body.snr'),
        minMax: 'xx - xx',
        average: 'xx',
        latestResult: {
          value: 'xx',
        },
        numberOfTests: 5,
      },
    ],
    [t],
  );

  const table = useReactTable({ data, columns, getCoreRowModel: getCoreRowModel() });
  return (
    <div className='flex h-full flex-col'>
      <div className='bg-surface-body flex h-[64px] items-center gap-x-3 px-6'>
        <div className='w-[360px]'>
          <AxonSelect>
            <AxonSelectTrigger className='font-medium'>
              <AxonSelectValue placeholder='Client statistics - Wayne-iPhone14Pro-391422' />
            </AxonSelectTrigger>
            <AxonSelectContent>
              <AxonSelectItem value='Wayne'>
                {t('customer:healthCheck.wheel.client.drawer.statistics.clientStats')}
              </AxonSelectItem>
            </AxonSelectContent>
          </AxonSelect>
        </div>
        <AxonButton variant={'ghost'} className='ml-auto' onClick={onButtonClick}>
          <Repeat className='text-component-hyperlink mr-2 size-4' />{' '}
          <p className='text-component-hyperlink font-book text-xs'>
            {t('customer:healthCheck.wheel.client.drawer.statistics.switchToSummary')}
          </p>
        </AxonButton>
        <AxonSheetClose>
          <AxonButton variant={'ghost'} size={'icon'}>
            <X className='size-4' />
          </AxonButton>
        </AxonSheetClose>
      </div>
      <AxonSeparator />
      <div className='bg-surface-body flex h-[64px] items-center gap-x-3 px-6'>
        <AxonInput
          placeholder={t('customer:healthCheck.wheel.client.drawer.statistics.searchByType')}
          startDecorator={<Search />}
          className='w-[360px]'
        />
        <div className='ml-auto'>
          <AxonSelect>
            <AxonSelectTrigger>
              <AxonSelectValue placeholder='21-01-2024 23:00 - 23:59 (Latest)' />
            </AxonSelectTrigger>
            <AxonSelectContent>
              <AxonSelectItem value='latest'>21-01-2024 23:00 - 23:59 (Latest)</AxonSelectItem>
            </AxonSelectContent>
          </AxonSelect>
        </div>
        <AxonButton variant={'accent'} startDecorator={<RotateCcw className='size-4' />}>
          {t('customer:healthCheck.wheel.client.drawer.statistics.runSpeedTest')}
        </AxonButton>
      </div>
      <AxonSeparator />
      <div className='bg-surface-section flex-1'>
        <AxonTableData table={table} showFooter={false} />
      </div>
    </div>
  );
};

export default ClientsStats;
