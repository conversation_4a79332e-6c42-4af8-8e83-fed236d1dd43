import useTabStore from '@/stores/tab.store';
import { useGetHealthCheckClientExtended } from 'services/HealthCheck';
import { useShallow } from 'zustand/react/shallow';
import get from 'lodash/get';
import { useGetCpeInfo } from 'services/CPEService';
import { useMemo, useState } from 'react';
import debounce from 'lodash/debounce';
import { useTranslation } from 'react-i18next';

export const ALL_VALUE_OPTION = 'all';

export const useClientTableAction = () => {
  const { t } = useTranslation();
  const { activeTab } = useTabStore(
    useShallow((state) => ({
      activeTab: state.activeTab,
      setTimeRange: state.setTimeRange,
    })),
  );
  const lineId = activeTab?.deviceId || activeTab?.customerId;
  const isDeviceTab = activeTab?.type === 'device';

  const { data: cpeInfo } = useGetCpeInfo(lineId ?? '', { enabled: !!lineId, staleTime: Infinity });
  const customerId: string = get(cpeInfo, 'data.customerId') ?? ''; // id of customer
  const deviceId: string = get(cpeInfo, 'data.id') ?? ''; // id of customer

  const { data: healthCheckClientExtended, ...rest } = useGetHealthCheckClientExtended(
    {
      customerId,
      deviceId: isDeviceTab ? deviceId : '',
    },
    {
      enabled: isDeviceTab ? !!deviceId : !!customerId,
      staleTime: 3 * 60 * 1000,
    },
  );

  const [filterState, setFilterState] = useState<{
    search: string;
    cpeId: string;
    deviceType: string;
    connectionInterface: string;
    parentalControl: string;
    status: 'online' | 'offline' | '' | typeof ALL_VALUE_OPTION;
  }>({
    search: '',
    cpeId: ALL_VALUE_OPTION,
    deviceType: ALL_VALUE_OPTION,
    connectionInterface: ALL_VALUE_OPTION,
    parentalControl: ALL_VALUE_OPTION,
    status: ALL_VALUE_OPTION,
  });

  const debouncedSearchInput = useMemo(() => {
    return debounce((value: string) => {
      setFilterState({ ...filterState, search: value });
    }, 500);
  }, [setFilterState, filterState]);

  const filteredData = useMemo(() => {
    const { results } = healthCheckClientExtended?.data || {};

    if (!results) {
      return [];
    }
    return results.filter((item) => {
      const { cpeId, deviceType, connectionInterface, parentalControl, status, search } = filterState;
      const isFilterCpeId = cpeId === '' || cpeId === ALL_VALUE_OPTION || item.cpeId === cpeId;
      const isFilterDeviceType =
        deviceType === '' || deviceType === ALL_VALUE_OPTION || item.deviceInfo.deviceType === deviceType;
      const isFilterConnectionInterface =
        connectionInterface === '' ||
        connectionInterface === ALL_VALUE_OPTION ||
        item.networkConnection.connectionInterface === connectionInterface;
      const parentalControlEnabled = parentalControl === 'enabled';
      const isFilterParentalControl =
        parentalControl === '' ||
        parentalControl === ALL_VALUE_OPTION ||
        item.parentalControls.some((control) => parentalControlEnabled === control.enabled);

      const isFilterStatus =
        status === '' || status === ALL_VALUE_OPTION || status === (item.deviceInfo.isOnline ? 'online' : 'offline');
      const isFilterSearch = search === '' || item.deviceInfo.deviceName.toLowerCase().includes(search.toLowerCase());
      return (
        isFilterCpeId &&
        isFilterDeviceType &&
        isFilterConnectionInterface &&
        isFilterParentalControl &&
        isFilterStatus &&
        isFilterSearch
      );
    });
  }, [healthCheckClientExtended, filterState]);

  const selectionOptions = useMemo(() => {
    const { cpeIds, deviceTypes, connectionInterfaces } = healthCheckClientExtended?.data || {};
    return {
      cpeIdsOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.allCpe'),
          value: ALL_VALUE_OPTION,
        },
        ...(cpeIds || []),
      ],
      deviceTypesOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.allType'),
          value: ALL_VALUE_OPTION,
        },
        ...(deviceTypes || []),
      ],
      connectionInterfacesOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.allConnection'),
          value: ALL_VALUE_OPTION,
        },
        ...(connectionInterfaces || []),
      ],
      parentalControlOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.parental'),
          value: ALL_VALUE_OPTION,
        },
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.enable'),
          value: 'enabled',
        },
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.disable'),
          value: 'disabled',
        },
      ],
    };
  }, [healthCheckClientExtended, t]);

  return {
    ...rest,
    data: healthCheckClientExtended,
    filteredData,
    filterState,
    setFilterState,
    selectionOptions,
    debouncedSearchInput,
  };
};
