import { LogProps } from '@/components/Logs/LogDrawer';
import { formatDate } from 'services/Utils';
import { router } from 'ui/UIAssets';
import { AxonCard, AxonImage } from 'ui/UIComponents';
import { useTheme } from 'ui/UIProviders';

export const Logs = ({ logs }: LogProps) => {
  const { theme } = useTheme();
  const isDarkmode = theme === 'dark';

  return (
    <div className='bg-surface-section grid h-full grid-cols-3 gap-4 p-6'>
      {logs.map((item, i) => (
        <AxonCard
          key={i}
          className='relative h-[328px]'
          style={{ clipPath: 'polygon(0 0, 100% 0, 100% 87%, 88% 100%, 0 100%)' }}>
          <span className='absolute bottom-0 right-0 origin-bottom-right'>
            <svg xmlns='http://www.w3.org/2000/svg' width='37' height='40' viewBox='0 0 37 40' fill='none'>
              <path
                d='M0 8.88889C0 3.97969 3.68121 0 8.22222 0H37V3.10219C37 3.35766 36.9022 3.60344 36.7267 3.7891L2.5 40H0V8.88889Z'
                fill='url(#paint0_linear_5599_10856)'
              />
              <defs>
                <linearGradient
                  id='paint0_linear_5599_10856'
                  x1='18.5'
                  y1='0'
                  x2='18.5'
                  y2='40'
                  gradientUnits='userSpaceOnUse'>
                  <stop offset='0.4' stopColor={"isDarkmode ? '#fff' : '#000'"} stopOpacity='0.16' />
                  <stop offset='1' stopColor={isDarkmode ? '#fff' : '#000'} stopOpacity='0.02' />
                </linearGradient>
              </defs>
            </svg>
          </span>
          <div className='flex h-full flex-col gap-y-4 p-6'>
            <div className='header-card flex flex-row items-center'>
              <div className={`border-gradient-border bg-component-pill-babyblue rounded-xl border px-3 py-1`}>
                <p className='font-book text-content-primary text-sm'>Event</p>
              </div>
              <div className='ml-auto'>
                <AxonImage src={router} alt='Evetn Image' size={'xs'} className='p-1' />
              </div>
            </div>
            <div className='flex flex-1 flex-col gap-y-2'>
              <p className='text-content-primary text-xl font-medium'>{item.eventType}</p>
              <p className='text-content-primary scrollbar-sm h-16 overflow-auto opacity-75'>{item.description}</p>
            </div>
            {/* <div className='flex flex-row items-center gap-4'>
                    <AxonButton variant='default'>Open</AxonButton>
                  </div> */}
            <div className='mt-auto text-xs'>
              <p>
                <span className='text-muted-foreground'>Last modified on </span>
                <span className='font-semibold'>{formatDate(item.timeStamp || 0, 'MMM D, YYYY, hh:mm a')}</span>
              </p>
            </div>
          </div>
        </AxonCard>
      ))}
    </div>
  );
};
