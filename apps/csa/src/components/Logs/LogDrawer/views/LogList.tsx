import { LogProps } from '@/components/Logs/LogDrawer';
import {
  ColumnDef,
  getCoreRowModel,
  getExpandedRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { formatDate } from 'services/Utils';
import { AxonTableData } from 'ui/UIComponents';

export const LogList = ({ logs }: LogProps) => {
  const columns: ColumnDef<LogProps['logs'][number]>[] = [
    {
      accessorKey: 'category',
      header: 'Category',
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: 'source',
      header: 'Source',
      cell: (info) => info.row.original.source || info.row.original.deviceId || '-',
    },
    { accessorKey: 'eventType', header: 'Event', cell: (info) => info.getValue() },
    {
      accessorKey: 'timeStamp',
      header: 'Occured At',
      cell: (info) => {
        const value = info.getValue<LogProps['logs'][number]['timeStamp']>();
        return formatDate(value, 'MMM D, YYYY, HH:mm');
      },
    },
    { accessorKey: 'resolvedAt', header: 'Resolved at (beta)', cell: (info) => info.getValue() },
  ];

  const table = useReactTable({
    data: logs,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return <AxonTableData table={table} showFooter={false} />;
};
