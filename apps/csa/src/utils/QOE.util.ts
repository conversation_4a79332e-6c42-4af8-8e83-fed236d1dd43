export enum HealthCheckServiceStatusEnum {
  STABLE = 'Stable',
  UNSTABLE = 'Unstable',
  VERY_UNSTABLE = 'Very Unstable',
  UNKNOWN = 'Unknown',
}

export const convertQOEStatus = (status: number | null): HealthCheckServiceStatusEnum => {
  if (typeof status !== 'number') return HealthCheckServiceStatusEnum.UNKNOWN;

  if (status >= 4) {
    return HealthCheckServiceStatusEnum.STABLE;
  }

  if (status >= 2) {
    return HealthCheckServiceStatusEnum.UNSTABLE;
  }

  if (status >= 0) {
    return HealthCheckServiceStatusEnum.VERY_UNSTABLE;
  }

  return HealthCheckServiceStatusEnum.UNKNOWN;
};
