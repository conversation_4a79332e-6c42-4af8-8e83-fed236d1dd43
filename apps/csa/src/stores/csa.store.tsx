import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { devtools } from 'zustand/middleware';

interface CsaStore {
  isSearchOpen: boolean;
  setIsSearchOpen: (isOpen: boolean) => void;
  toggleSearch: () => void;
}

const usaCsaStore = create<CsaStore>()(
  devtools(
    immer((set) => ({
      isSearchOpen: false,
      setIsSearchOpen: (isOpen) => set({ isSearchOpen: isOpen }, undefined, 'setIsSearchOpen'),
      toggleSearch: () => set((state) => ({ isSearchOpen: !state.isSearchOpen }), undefined, 'toggleSearch'),
    })),
    { name: 'csaStore', store: 'csaStore' },
  ),
);

export default usaCsaStore;
